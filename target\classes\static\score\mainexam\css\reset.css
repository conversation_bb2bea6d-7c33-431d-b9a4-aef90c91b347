.layui-form-label {
  width: 115px;
  padding: 0;
  color: #83889D;
  text-align: left;
  height: 34px;
  color: #1D2129;
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
}
.layui-form-label.w70 {
  width: 70px;
}
.layui-input-block {
  min-height: 34px;
}
.layui-form-radio {
  margin-top: 3px;
}
.layui-form-item {
  padding-left: 0;
  margin-bottom: 24px;
}
.layui-form-item .layui-inline {
  margin-right: 0;
}
.layui-input,
.layui-textarea,
.layui-select {
  height: 34px;
  border-radius: 4px;
  border-color: #E5E6EB;
}
.layui-input {
  border-color: #E5E6EB;
  border-radius: 4px;
}
.layui-input::placeholder {
  color: #BCBCC5;
}
.layui-form-select dl dd.layui-this {
  background-color: #4C85FA;
}
.layui-btn {
  display: block;
  width: 100px;
  height: 34px;
  border-radius: 4px;
  text-align: center;
  line-height: 34px;
  font-size: 16px;
  cursor: pointer;
  float: left;
}
.layui-btn.layui-chaoxing-default-btn {
  border: 1px dashed #E5E6EB;
  border-radius: 4px;
  background: #EEEFF2;
  width: 110px;
  height: 34px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  text-align: center;
  line-height: 30px;
  padding: 0;
}
.layui-btn.layui-chaoxing-default-btn:hover {
  opacity: 1;
  background: #EFF1FF;
  border: 1px dashed #4A7CFE;
  border-radius: 4px;
}
.layui-btn.layui-chaoxing-default-btn:active {
  opacity: 1;
  background: #D3E0FF;
  border: 1px dashed #4A7CFE;
  border-radius: 4px;
}
.layui-btn.btn-cancel {
  background: #F5F6F8;
  border: 1px solid #4C85FA;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: #4C85FA;
}
.layui-btn.btn-related {
  border: 1px solid #525669;
  color: #525669;
  background: none;
}
.layui-btn.btn-search {
  background: #525669;
  color: #FFFFFF;
}
.layui-btn.btn-save {
  background: #4C85FA;
  color: #FFFFFF;
}
.layui-form-radio {
  margin: 3px 10px 0 0;
  padding-right: 12px;
}
.layui-form-radio * {
  color: #4E5969;
}
.layui-form-radio > i {
  margin-right: 10px;
}
.layui-form-radioed > i {
  color: #4C85FA;
}
.layui-form-radioed > i,
.layui-form-radio > i:hover {
  color: #4C85FA;
}
.layui-form-checkbox {
  margin: 0 10px 0 0;
}
.layui-form-checkbox[lay-skin="primary"] span {
  color: #4E5969;
  font-size: 14px;
  line-height: 16px;
}
.layui-form-checkbox[lay-skin="primary"] {
  padding-left: 24px;
  min-width: 16px;
  min-height: 16px;
}
.layui-form-checkbox[lay-skin="primary"] i {
  margin-right: 6px;
}
.layui-form-checkbox[lay-skin="primary"]:hover i {
  border-color: #4C85FA;
  color: #fff;
}
.layui-form-checked[lay-skin="primary"] i {
  border-color: #4C85FA !important;
  background-color: #4C85FA;
}
.layui-textarea::placeholder {
  color: #BCBCC5;
}
.layui-upload-drag {
  padding: 6px 15px;
  margin-right: 10px;
}
.layui-upload-drag .layui-icon {
  color: #4C85FA;
}
.uploadIntro p {
  color: #BCBCC5;
  margin: 5px 0px;
}
.layui-form-onswitch {
  border-color: #BCBCC5;
  background-color: #BCBCC5;
}
.layui-table {
  color: #4E5969;
  margin: 0;
}
.layui-table-view .layui-table th,
.layui-table-view .layui-table td {
  border-color: #E8EBF1;
}
.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even),
.layui-table tbody tr:hover,
.layui-table-hover,
.layui-table-click {
  border-color: #E8EBF3;
}
.layui-table tr {
  height: 40px;
}
.layui-table-view .layui-table tr th > div {
  margin: -5px 0px;
  height: 40px;
  line-height: 40px;
}
.layui-table-view .layui-table tr th:nth-child(1) > div {
  border: none;
}
.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even) {
  background: #F7F8FA;
}
.layui-table thead tr,
.layui-table-header {
  background: #F2F4F7;
  color: #8A8B99;
  font-size: 14px;
}
.layui-table-click {
  background: none;
}
.j-table table tbody tr {
  transition: none;
  -webkit-transition: none;
}
.j-table table tbody tr:nth-child(even) {
  background: #F7F8FA;
}
.j-table table tbody tr:nth-child(even) td {
  border-right: 1px solid #F7F8FA !important;
}
.j-table table tbody tr:nth-child(even) td:hover {
  border-right: 1px solid #DDE7FF !important;
}
.j-table table tbody tr:nth-child(even):hover {
  background: #DDE7FF;
}
.j-table table tbody tr:nth-child(even):hover td {
  border-right: 1px solid #DDE7FF !important;
}
.layui-table tr {
  transition: none !important;
  -webkit-transition: none !important;
}
.layui-table tr:nth-child(2n) {
  background: #FAFBFC;
}
.layui-table-view .layui-table td {
  padding: 4px 0;
}
.layui-table-view {
  margin: 0;
}
.layui-table-page {
  text-align: right;
}
.layui-table-view .layui-table tr th > div {
  color: #8A8B99;
  font-weight: 400;
  font-size: 14px;
}
.layui-table-view .layui-table tr th.icons > div {
  padding-left: 44px;
}
.layui-table-view .layui-table tr th.icons:first-child > div {
  padding-left: 0;
}
.layui-table-cell {
  font-size: 14px;
  color: #1D2129;
}
.layui-form-select .layui-edge {
  border: none;
  width: 12px;
  height: 12px;
  margin-top: -6px;
  background: url(../images/down-icon.png) no-repeat center;
}
.layui-form-select dl {
  padding: 0;
  border: none;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  top: 38px;
  border-radius: 4px;
}
.layui-form-select dl dt,
.layui-form-select dl dd {
  height: 34px;
  color: #4E5969;
  font-weight: 400;
  font-size: 14px;
  padding: 0 20px;
  line-height: 34px;
}
.layui-form-select dl dt:hover,
.layui-form-select dl dd:hover {
  background: #F7F8FA;
}
.layui-form-select dl dd.layui-this {
  background: #DDE7FF;
  color: #4A7CFE;
}
.layui-upload-choose {
  color: #86909C;
  font-weight: 400;
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  margin-bottom: 0;
}
.layui-form-item .layui-inline {
  margin-bottom: 0;
}
.layui-form-item .layui-input-inline {
  width: 800px;
}
.layui-form-item .layui-input-inline.input-lab {
  width: 360px;
}
.layui-form-radio > i {
  font-size: 18px;
}
.layui-form-radioed > i {
  background: url(../images/selected-radio.png) no-repeat center;
  background-size: 18px;
  color: transparent;
  box-shadow: none;
}
.layui-form-select .layui-select-title {
  width: 100%;
}
.layui-form-checkbox[lay-skin="primary"] i {
  border: 1px solid #E5E6EB;
  border-radius: 3px;
  box-sizing: border-box;
  box-sizing: -webkit-border-box;
}
.layui-form-checked[lay-skin="primary"] i {
  background: url(../images/checked-icon.png) no-repeat center;
  border: none;
  color: transparent;
}
.layui-form-checked[lay-skin="primary"] i:before {
  display: none;
}
.layui-form-select {
  width: 100%;
}
.layui-layer-hui {
  border-radius: 4px !important;
}
.layui-layer-hui .layui-layer-content {
  padding: 10px 25px !important;
}
.layui-table-view .layui-form-checkbox[lay-skin="primary"] i {
  width: 16px;
  height: 16px;
}
.layui-laydate tr .layui-this {
  background: #4A7CFE !important;
}
.layui-laydate-content tr td.laydate-selected {
  background: #E9F0FF !important;
}
.layui-laydate-footer span:hover {
  color: #4A7CFE !important;
}
.layui-laydate-header i:hover,
.layui-laydate-header span:hover {
  color: #4A7CFE !important;
}
.layui-btn-normal {
  background: #4A7CFE !important;
}
.layui-laydate .layui-laydate-list .layui-this {
  background: #4A7CFE !important;
}
.layui-btn-primary:hover {
  border-color: #4A7CFE !important;
  color: #4A7CFE;
}
.layui-layer-tips {
  margin-top: -12px !important;
}
.showImgEdit .layui-btn {
  width: auto;
}
.layui-fluid {
  padding: 15px;
}
.laydate-footer-btns span {
  border: none !important;
}
.layui-laydate .laydate-disabled {
  border: none !important;
}
.layui-layer-tips i.layui-layer-TipsR {
  left: -16px !important;
  transform: rotate(-90deg) !important;
  top: 12px !important;
}
.layui-btn {
  background-color: #4A7CFE;
}
.layui-btn-primary {
  background-color: #fff;
}
.layui-table-view .layui-form-radio > i {
  width: 18px;
  height: 18px;
}
.layui-form-radio > i {
  background: url(../images/default-radio.png) no-repeat center;
  background-size: 18px;
  color: transparent;
  box-shadow: none;
}
.layui-form-radioed > i {
  background: url(../images/selected-radio.png) no-repeat center;
  background-size: 18px;
  color: transparent;
  box-shadow: none;
}
.layui-form-radioed > i,
.layui-form-radio > i:hover {
  color: transparent;
}
.layui-layer-page .layui-layer-content {
  overflow: visible !important;
  height: auto !important;
}
.layui-radio-disbaled > i {
  background: url(../images/radio-disabled.png) no-repeat center;
  background-size: 18px;
  color: transparent !important;
  box-shadow: none;
}
.layui-table-grid-down { display: none; }