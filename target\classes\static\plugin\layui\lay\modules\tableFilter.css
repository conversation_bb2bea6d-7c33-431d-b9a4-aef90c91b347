/*table 过滤*/
.layui-table-filter {height:100%;cursor: pointer;position: absolute;right:15px;padding:0 5px;}
.layui-table-filter i {font-size: 12px;color: #ccc;}
.layui-table-filter:hover i {color: #666;}
.layui-table-filter.tableFilter-has i {color: #1E9FFF;}
.layui-table-filter-view {width:auto;min-width:120px;background:#FFFFFF;border: none;position:absolute;top:0px;left:0px;z-index:10000000;
    opacity:0;
    -webkit-opacity:0;
    -o-opacity:0;
    filter:alpha(opacity=0);
    box-shadow: 0px 3px 0px #e2e2e2;
    transition: all .3s cubic-bezier(.645,.045,.355,1);
    -webkit-transition: all .3s cubic-bezier(.645,.045,.355,1);
    -moz-transition: all .3s cubic-bezier(.645,.045,.355,1);
    -o-transition: all .3s cubic-bezier(.645,.045,.355,1);
    max-height:0;overflow:hidden;}
.layui-table-filter-view.show{
    max-height:200px;overflow:hidden;
    opacity:1;
    -webkit-opacity:1;
    -o-opacity:1;
    filter:alpha(opacity=100);
    box-shadow: 0px 3px 10px #e2e2e2;
}
.layui-table-filter-box {padding:10px;}
.layui-table-filter-box .loading {width: 100%;height: 100%;text-align: center;line-height: 150px;}
.layui-table-filter-box .loading i {font-size: 18px;}
.layui-table-filter-box input.layui-input {margin-bottom:10px;}
.layui-table-filter-box ul {border: 1px solid #eee;height:auto;overflow: auto;margin-bottom:10px;padding:5px 10px 5px 10px;}
.layui-table-filter-box ul li {padding:3px 0;}
.layui-table-filter-box ul.radio {padding:0px;}
.layui-table-filter-box ul.radio li {padding:0px;}
.layui-table-filter-box ul li .layui-form-radio {display: block;color:#666;margin:0px;padding:0px;transition: .1s linear;}
.layui-table-filter-box ul li .layui-form-radio div {display: block;padding:0 10px;}
.layui-table-filter-box ul li .layui-form-radio i {display: none;}
.layui-table-filter-box ul li .layui-form-radio:hover {background:#f9f9f9;}
.layui-table-filter-box ul li .layui-form-radio.layui-form-radioed {background:#717795;color: #fff;}