.borDer {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-item {
  display: flex !important;
  display: -webkit-flex !important;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 0;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 14px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
body {
  font-family: 'PingFang SC';
  background: #F7F8FA;
}
/*.main {*/
/*  padding: 22px 20px 20px;*/
/*}*/
.main .m-tab {
  width: 100%;
  height: 60px;
  border-radius: 8px;
  background: #FFF;
  margin-bottom: 20px;
}
.main .m-tab ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
}
.main .m-tab ul li {
  width: 156px;
  font-size: 16px;
  height: 60px;
  line-height: 60px;
  color: #474C59;
  font-style: normal;
  font-weight: 400;
  text-align: center;
  cursor: pointer;
  position: relative;
}
.main .m-tab ul li.cur {
  color: #4D88FF;
  font-weight: 500;
}
.main .m-tab ul li.cur:after {
  width: 48px;
  height: 4px;
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  margin-left: -24px;
  border-radius: 2px;
  background: #3A8BFF;
}
.main .content {
  width: 100%;
  min-height: calc(100vh);
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 30px;
}
.main .content .c-top {
  position: relative;
  width: 100%;
}
.main .content .c-top .btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 104px;
  height: 36px;
  border-radius: 6px;
  background-color: #4D88FF;
  cursor: pointer;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  color: #FFFFFF;
}
.main .content h2 {
  color: #3C6EDB;
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 21px;
  margin-bottom: 30px;
  padding-left: 10px;
  position: relative;
}
.main .content h2:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 4px;
  height: 16px;
  background-color: #3A8BFF;
}
.add-lab {
  width: 100%;
  height: 36px;
  margin-bottom: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.add-lab span {
  width: 92px;
  height: 36px;
  border-radius: 6px;
  background: #3A8BFF;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
}
.layui-form .layui-form-item {
  margin-bottom: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.layui-form .layui-form-item .limit-switch {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.layui-form .layui-form-item .limit-switch span {
  line-height: 34px;
  color: #c9cdd4;
  margin-left: 10px;
}
.layui-form .layui-form-item .limit-switch .layui-form-switch {
  border-radius: 3px;
  background: #D2D3D8;
  height: 14px;
  line-height: 14px;
  min-width: 28px;
  padding: 0 0;
  margin-top: 10px;
  border: none;
}
.layui-form .layui-form-item .limit-switch .layui-form-switch i {
  left: 2px;
  top: 2px;
  width: 12px;
  height: 10px;
  border-radius: 1px;
  background: #FFF;
  margin-left: 0;
}
.layui-form .layui-form-item .limit-switch .layui-form-onswitch {
  border-radius: 3px;
  background: #537AF6;
}
.layui-form .layui-form-item .limit-switch .layui-form-onswitch i {
  left: 100%;
  margin-left: -14px;
  background-color: #fff;
}
.layui-form .layui-form-item .limit-switch .tit {
  margin-left: 10px;
  padding-top: 9px;
}
.layui-form .layui-form-item .limit-switch .tit h4 {
  color: #131B26;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 4px;
}
.layui-form .layui-form-item .limit-switch .tit p {
  color: #8A8B99;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
}
.layui-form .layui-form-item .layui-form-label {
  color: #1D2129;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-right: 16px;
  height: 34px;
  line-height: 34px;
  width: 84px;
}
.layui-form .layui-form-item .layui-form-label em {
  font-size: 16px;
  color: #E23131;
  display: inline-block;
  margin-right: 5px;
}
.layui-form .layui-form-item .times {
  margin-right: 16px;
  width: 240px;
  height: 34px;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: pointer;
  background: url(../images/times.png) no-repeat 220px center;
  background-size: 12px;
}
.layui-form .layui-form-item .times .layui-input {
  background-color: transparent;
  color: #4E5969;
  font-size: 14px;
  cursor: pointer;
}
.layui-form .layui-form-item .layui-input-block {
  margin-left: 0;
}
.layui-form .layui-form-item .layui-input-block.w240 {
  width: 240px;
}
.layui-form .layui-form-item .layui-input-block.w60 {
  width: 60px;
}
.layui-form .layui-form-item .layui-input-block.w275 {
  width: 275px;
}
.layui-form .layui-form-item .layui-input-block .lab {
  height: 34px;
  margin-bottom: 6px;
  position: relative;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.layui-form .layui-form-item .layui-input-block .lab:last-child {
  margin-bottom: 0;
}
.layui-form .layui-form-item .layui-input-block .lab .layui-form-radio {
  margin: 0;
}
.layui-form .layui-form-item .layui-input-block .lab .introIcon:hover .bubble {
  display: block;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble {
  display: none;
  position: absolute;
  left: 100%;
  top: 50%;
  margin-top: -15px;
  padding-left: 17px;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble:after {
  content: '';
  position: absolute;
  left: 13px;
  top: 50%;
  width: 4px;
  height: 10px;
  margin-top: -5px;
  background: url(../images/triangle.png) no-repeat left center;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble span {
  display: block;
  border-radius: 4px;
  background: #F2F5F7;
  height: 29px;
  padding: 0 16px;
  font-size: 12px;
  line-height: 29px;
  color: #737B86;
  white-space: nowrap;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble span em {
  color: #3A8BFF;
  cursor: pointer;
  margin-left: 9px;
}
.introIcon {
  width: 12px;
  height: 12px;
  background: url(../images/tips-icons.png) no-repeat center;
  background-size: 12px;
  cursor: pointer;
  display: inline-block;
}
#addPoup {
  width: 614px;
}
#addPoup .popup-con .layui-form .layui-form-label {
  color: #474C59;
  width: 110px;
}
#addPoup .popup-con .layui-form .layui-form-item.wrong {
  position: relative;
}
#addPoup .popup-con .layui-form .layui-form-item.wrong .layui-input {
  border: 1px solid #F33131;
}
#addPoup .popup-con .layui-form .layui-form-item.wrong .layui-input:hover {
  border: 1px solid #F33131 !important;
}
#addPoup .popup-con .layui-form .layui-form-item.wrong .layui-form-selected .layui-input {
  border: 1px solid #F33131 !important;
}
#addPoup .popup-con .layui-form .layui-form-item.wrong .error {
  display: block;
}
#addPoup .popup-con .layui-form .layui-form-item .error {
  position: absolute;
  left: 126px;
  top: 38px;
  line-height: 14px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  color: #F33131;
  display: none;
}
#addPoup .popup-con .layui-form .layui-form-item .times {
  background: url(../images/calendar-icons.png) no-repeat 214px center;
  background-size: 18px;
}
.subitem {
  width: 640px;
}
.subitem .popup-con .add-lab {
  margin-bottom: 13px;
}
.subitem .layui-table-view .layui-table th:last-child {
  border-right: none;
}
.hide {
  display: none !important;
}
#addPoups {
  width: 614px;
}
#addPoups .popup-con .layui-form .layui-form-label {
  color: #474C59;
  width: 130px;
}
#addPoups .popup-con .layui-form .layui-form-item.wrong {
  position: relative;
}
#addPoups .popup-con .layui-form .layui-form-item.wrong .layui-input {
  border: 1px solid #F33131;
}
#addPoups .popup-con .layui-form .layui-form-item.wrong .layui-input:hover {
  border: 1px solid #F33131 !important;
}
#addPoups .popup-con .layui-form .layui-form-item.wrong .layui-form-selected .layui-input {
  border: 1px solid #F33131 !important;
}
#addPoups .popup-con .layui-form .layui-form-item.wrong .error {
  display: block;
}
#addPoups .popup-con .layui-form .layui-form-item .error {
  position: absolute;
  left: 126px;
  top: 38px;
  line-height: 14px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  color: #F33131;
  display: none;
}
#addPoups .popup-con .layui-form .layui-form-item .times {
  background: url(../images/calendar-icons.png) no-repeat 214px center;
  background-size: 18px;
}
.levelAdd {
  width: 800px !important;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .layui-form-switch {
  border-radius: 3px;
  background: #D2D3D8;
  height: 14px;
  line-height: 14px;
  min-width: 28px;
  padding: 0 0;
  margin-top: 10px;
  border: none;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .layui-form-switch i {
  left: 2px;
  top: 2px;
  width: 12px;
  height: 10px;
  border-radius: 1px;
  background: #FFF;
  margin-left: 0;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .layui-form-onswitch {
  border-radius: 3px;
  background: #537AF6;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .layui-form-onswitch i {
  left: 100%;
  margin-left: -14px;
  background-color: #fff;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .tit {
  margin-left: 10px;
  padding-top: 9px;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .tit h4 {
  color: #131B26;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 4px;
}
.levelAdd .popup-con .layui-form .layui-form-item .limit-switch .tit p {
  color: #8A8B99;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
}
.levelAdd .popup-con .layui-form .layui-form-item .eligibility {
  margin-top: 15px;
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.levelAdd .popup-con .layui-form .layui-form-item .eligibility .name {
  margin-right: 10px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable {
  margin-top: 15px;
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable:first-child .operate {
  display: none;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable:last-child .operate {
  display: flex !important;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .layui-form-radio {
  margin: 3px 6px 0 0;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .layui-form-radio > i {
  margin-right: 4px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .w128 {
  width: 120px;
  flex-shrink: 0;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .name {
  flex-shrink: 0;
  margin-right: 8px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .radio-list {
  flex-shrink: 0;
  width: 146px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .select {
  margin-right: 20px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .input {
  margin-right: 20px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .operate {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 0px;
  height: 34px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .operate .delet {
  width: 16px;
  height: 16px;
  background: url(../images/delete-icons.png) no-repeat center;
  cursor: pointer;
  margin-right: 16px;
}
.levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable .operate .add {
  width: 16px;
  height: 16px;
  background: url(../images/add-icons.png) no-repeat center;
  cursor: pointer;
}
.levelAdd .popup-con .layui-form .layui-form-item .add-level {
  margin-top: 18px;
  height: 18px;
  line-height: 18px;
}
.levelAdd .popup-con .layui-form .layui-form-item .add-level span {
  color: #3A8BFF;
  font-size: 13px;
  font-weight: normal;
  cursor: pointer;
  padding-left: 24px;
  background: url(../images/set-blue.png) no-repeat left center;
}
.scrollBox::-webkit-scrollbar {
  width: 8px;
  height: 10px;
}
.scrollBox::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: #DADFE6;
}
.scrollBox::-webkit-scrollbar-track {
  border-radius: 6px;
}
.scrollBox1::-webkit-scrollbar {
  width: 4px;
  height: 10px;
}
.scrollBox1::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #DADFE6;
}
.scrollBox1::-webkit-scrollbar-track {
  border-radius: 10px;
}
#gsbjsubitem {
  width: 845px;
}
#gsbjsubitem .popup-con .layui-form .layui-form-label {
  font-size: 16px;
  width: auto;
}
#gsbjsubitem .popup-con .lable h3 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #131b26;
  line-height: 22px;
  margin-bottom: 16px;
}
#gsbjsubitem .popup-con .lable h3 em {
  font-size: 16px;
  color: #e23131;
  display: inline-block;
  margin-right: 5px;
}
#gsbjsubitem .popup-con .lable .formula {
  border-radius: 8px;
  border: 1px solid #8CBBFF;
  background: #F0F6FF;
  min-height: 80px;
  position: relative;
  margin-bottom: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .lable .formula:after {
  content: '';
  position: absolute;
  left: 86px;
  top: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  background-color: #8CBBFF;
}
#gsbjsubitem .popup-con .lable .formula .name {
  width: 86px;
  flex-shrink: 0;
  text-align: center;
  font-size: 14px;
  color: #4e5969;
  font-style: normal;
  font-weight: 500;
  border-right: 1px solid #8CBBFF;
}
#gsbjsubitem .popup-con .lable .formula .f-con {
  flex: 1;
  padding: 10px 16px;
  position: relative;
}
#gsbjsubitem .popup-con .lable .formula .f-con:after {
  content: '1、使用参与计算的成绩分项 2、选择分项对应的计分级制 3、使用计算键盘可灵活配置计算规则 ';
  position: absolute;
  left: 15px;
  top: 50%;
  margin-top: -8px;
}
#gsbjsubitem .popup-con .lable .formula .f-con.cons:after {
  display: none;
}
#gsbjsubitem .popup-con .lable .formula .f-con span {
  float: left;
  margin: 0 5px;
  padding: 0 14px;
  width: auto;
  height: 28px;
  background: #4D88FF;
  border-radius: 15px;
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 10px;
}
#gsbjsubitem .popup-con .lable .formula .f-con .sign {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
  padding: 0;
}
#gsbjsubitem .popup-con .lable .formula .f-con .num {
  padding: 0;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
}
#gsbjsubitem .popup-con .lable .formula .f-con .num.spot {
  margin: 0;
}
#gsbjsubitem .popup-con .grade-keyboard {
  border-radius: 8px;
  background: #F7F8FA;
  padding: 24px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top {
  width: 100%;
  height: 20px;
  margin-bottom: 15px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .titles {
  font-size: 15px;
  color: #131b26;
  margin-right: 14px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .tips {
  border-radius: 9.6px;
  border: 1px solid #F2F2F2;
  background: #F2F4F7;
  padding: 0 10px;
  height: 20px;
  line-height: 20px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .tips span {
  display: inline-block;
  background: url(../images/tips-icons.png) no-repeat left center;
  font-size: 12px;
  color: #737b86;
  padding-left: 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch {
  position: absolute;
  top: 2px;
  right: 0;
  height: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch span {
  color: #8a8b99;
  font-size: 14px;
  line-height: 20px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch {
  margin-top: 0;
  height: 14px;
  line-height: 14px;
  min-width: 26px;
  padding: 0;
  margin-left: 10px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch i {
  width: 10px;
  height: 10px;
  left: 2px;
  top: 2px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch {
  background-color: #3A8BFF;
  border-color: #3A8BFF;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch i {
  margin-left: 0;
  left: 14px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons {
  overflow-x: auto;
  padding-bottom: 5px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item {
  width: 333px;
  flex-shrink: 0;
  margin-right: 67px;
  border-radius: 4px;
  border: 1px solid #E4E8F0;
  background: #FFF;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con {
  border-bottom: 1px solid #E4E8F0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .left {
  flex-shrink: 0;
  flex: 1;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 40px;
  padding: 0 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .left .name {
  background: url(../images/check-icon.png) no-repeat left center;
  font-size: 14px;
  color: #474c59;
  padding-left: 28px;
  cursor: pointer;
  height: 100%;
  line-height: 40px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .left .name.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right {
  flex-shrink: 0;
  width: 0;
  height: 117px;
  overflow-y: auto;
  display: none;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul {
  padding: 3px 0;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li {
  margin-bottom: 3px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li .name {
  background: url(../images/check-icon.png) no-repeat left center;
  font-size: 14px;
  color: #474c59;
  padding-left: 28px;
  cursor: pointer;
  margin-right: 10px;
  max-width: 70px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li .name.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right .select.w102 {
  width: 102px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item.showMore .i-con .left {
  width: 101px;
  border-right: 1px solid #E4E8F0;
  height: 117px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item.showMore .i-con .left .name {
  line-height: 117px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item.showMore .i-con .right {
  width: 232px;
  display: block;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-bottom {
  padding: 3px 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard h3 {
  font-size: 15px;
  color: #131b26;
  line-height: 21px;
  margin-top: 2px;
  margin-bottom: 16px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con {
  overflow: hidden;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span {
  float: left;
  height: 42px;
  border: 1px solid #E4E8F0;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-bottom: 5px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  width: 130px;
  margin-right: 20px;
  line-height: 40px;
  padding: 0 14px;
  text-align: left;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span:nth-child(5n) {
  margin-right: 0;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign {
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.spot {
  position: relative;
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.spot:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 4px;
  height: 4px;
  background: #4E5969;
  margin-left: -2px;
  margin-top: -2px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.spot:hover:after {
  background-color: #4D88FF;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.zero {
  width: 280px;
  height: 42px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span:hover {
  background: #E1EBFF;
  color: #4D88FF;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span:last-child {
  margin-right: 0;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.lbracket {
  background: #FFFFFF url(../images/sign-lkh.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.lbracket:hover {
  background: #E1EBFF url(../images/sign-lkh-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.rbracket {
  background: #FFFFFF url(../images/sign-rkh.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.rbracket:hover {
  background: #E1EBFF url(../images/sign-rkh-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.delet {
  background: #FFFFFF url(../images/sign-back.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.delet:hover {
  background: #E1EBFF url(../images/sign-back-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-add {
  background: #FFFFFF url(../images/sign-add.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-add:hover {
  background: #E1EBFF url(../images/sign-add-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle {
  background: #FFFFFF url(../images/sign-jian.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle:hover {
  background: #E1EBFF url(../images/sign-jian-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-mul {
  background: #FFFFFF url(../images/sign-ceng.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-mul:hover {
  background: #E1EBFF url(../images/sign-ceng-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-except {
  background: #FFFFFF url(../images/sign-chu.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-except:hover {
  background: #E1EBFF url(../images/sign-chu-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo {
  width: 845px;
}
#gsbjsubitemTwo .popup-con .layui-form.flex-form {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .layui-form.flex-form .layui-form-item {
  flex: 1;
}
#gsbjsubitemTwo .popup-con .layui-form .layui-form-label {
  font-size: 16px;
  width: auto;
}
#gsbjsubitemTwo .popup-con .lable h3 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #131b26;
  line-height: 22px;
  margin-bottom: 16px;
}
#gsbjsubitemTwo .popup-con .lable h3 em {
  font-size: 16px;
  color: #e23131;
  display: inline-block;
  margin-right: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula {
  border-radius: 8px;
  border: 1px solid #8CBBFF;
  background: #F0F6FF;
  min-height: 80px;
  position: relative;
  margin-bottom: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .lable .formula:after {
  content: '';
  position: absolute;
  left: 86px;
  top: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  background-color: #8CBBFF;
}
#gsbjsubitemTwo .popup-con .lable .formula .name {
  width: 86px;
  flex-shrink: 0;
  text-align: center;
  font-size: 14px;
  color: #4e5969;
  font-style: normal;
  font-weight: 500;
  border-right: 1px solid #8CBBFF;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con {
  flex: 1;
  padding: 10px 16px;
  position: relative;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con:after {
  content: '1、使用参与计算的成绩分项 2、选择分项对应的计分级制 3、使用计算键盘可灵活配置计算规则 ';
  position: absolute;
  left: 15px;
  top: 50%;
  margin-top: -8px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con.cons:after {
  display: none;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con span {
  float: left;
  margin: 0 5px;
  padding: 0 14px;
  width: auto;
  height: 24px;
  background: #4D88FF;
  border-radius: 15px;
  text-align: center;
  line-height: 24px;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con .sign {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
  padding: 0;
  margin-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con .num {
  padding: 0;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
  margin-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con .num.spot {
  margin: 0;
}
#gsbjsubitemTwo .popup-con .grade-keyboard {
  border-radius: 8px;
  background: #F7F8FA;
  padding: 24px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top {
  width: 100%;
  height: 20px;
  margin-bottom: 15px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .titles {
  font-size: 15px;
  color: #131b26;
  margin-right: 14px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .tips {
  border-radius: 9.6px;
  border: 1px solid #F2F2F2;
  background: #F2F4F7;
  padding: 0 10px;
  height: 20px;
  line-height: 20px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .tips span {
  display: inline-block;
  background: url(../images/tips-icons.png) no-repeat left center;
  font-size: 12px;
  color: #737b86;
  padding-left: 15px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch {
  position: absolute;
  top: 2px;
  right: 0;
  height: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch span {
  color: #8a8b99;
  font-size: 14px;
  line-height: 20px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch {
  margin-top: 0;
  height: 14px;
  line-height: 14px;
  min-width: 26px;
  padding: 0;
  margin-left: 10px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch i {
  width: 10px;
  height: 10px;
  left: 2px;
  top: 2px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch {
  background-color: #3A8BFF;
  border-color: #3A8BFF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch i {
  margin-left: 0;
  left: 14px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons {
  overflow-x: auto;
  padding-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul {
  padding: 3px 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li {
  margin-bottom: 3px;
  width: 356px;
  flex-shrink: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  margin-right: 20px;
  height: 42px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 15px;
  border-radius: 4px;
  border: 1px solid #E4E8F0;
  background: #FFF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .layui-input,
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .layui-textarea,
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .layui-select {
  height: 28px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .name {
  background: url(../images/check-icon.png) no-repeat left center;
  font-size: 14px;
  color: #474c59;
  padding-left: 28px;
  cursor: pointer;
  margin-right: 10px;
  max-width: 70px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .name.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .select.w102 {
  width: 102px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard h3 {
  font-size: 15px;
  color: #131b26;
  line-height: 21px;
  margin-top: 2px;
  margin-bottom: 16px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con {
  overflow: hidden;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span {
  float: left;
  height: 42px;
  border: 1px solid #E4E8F0;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-bottom: 5px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  width: 130px;
  margin-right: 20px;
  line-height: 40px;
  padding: 0 14px;
  text-align: left;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span:nth-child(5n) {
  margin-right: 0;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign {
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.spot {
  position: relative;
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.spot:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 4px;
  height: 4px;
  background: #4E5969;
  margin-left: -2px;
  margin-top: -2px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.spot:hover:after {
  background-color: #4D88FF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.zero {
  width: 280px;
  height: 42px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span:hover {
  background: #E1EBFF;
  color: #4D88FF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span:last-child {
  margin-right: 0;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.lbracket {
  background: #FFFFFF url(../images/sign-lkh.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.lbracket:hover {
  background: #E1EBFF url(../images/sign-lkh-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.rbracket {
  background: #FFFFFF url(../images/sign-rkh.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.rbracket:hover {
  background: #E1EBFF url(../images/sign-rkh-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.delet {
  background: #FFFFFF url(../images/sign-back.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.delet:hover {
  background: #E1EBFF url(../images/sign-back-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-add {
  background: #FFFFFF url(../images/sign-add.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-add:hover {
  background: #E1EBFF url(../images/sign-add-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle {
  background: #FFFFFF url(../images/sign-jian.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle:hover {
  background: #E1EBFF url(../images/sign-jian-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-mul {
  background: #FFFFFF url(../images/sign-ceng.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-mul:hover {
  background: #E1EBFF url(../images/sign-ceng-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-except {
  background: #FFFFFF url(../images/sign-chu.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-except:hover {
  background: #E1EBFF url(../images/sign-chu-cur.png) no-repeat 14px center;
}
#subitemadd {
  width: 640px;
}
#subitemadd .popup-con .layui-form .layui-form-label {
  color: #474C59;
  width: 130px;
}
.addInform {
  width: 640px;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegments {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegments .w50 {
  width: 50px;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegments span {
  margin-left: 4px;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegments .select {
  width: 146px;
  margin: 0 16px;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment {
  border: 1px solid #D4D6D9;
  border-radius: 4px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .w64 {
  width: 64px;
  border: none;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-input:hover {
  border: none !important;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-input:focus {
  border: none !important;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-form-selected .layui-input {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-select-title .layui-input {
  border-radius: 0;
  border-top: none;
  border-bottom: none;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-select-title .layui-input:hover {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-select-title .layui-input:focus {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
.addInform .popup-con .layui-form .layui-form-item .fractionalSegment .layui-select-title .layui-input:active {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
.addInform .popup-con .layui-form .layui-form-label {
  color: #474C59;
  width: 130px;
}
.save-settings {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: center;
  margin-top: 40px;
}
.save-settings span {
  width: 104px;
  height: 36px;
  border-radius: 6px;
  background-color: #4D88FF;
  cursor: pointer;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  color: #FFFFFF;
}
.calculRuleSet .layui-form .layui-form-item .layui-form-label {
  width: auto;
  margin-right: 18px;
  white-space: nowrap;
}
#calcAddPoups {
  width: 880px;
}
#calcAddPoups .popup-con .layui-form {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#calcAddPoups .popup-con .layui-form .layui-form-item {
  flex: 1;
}
#calcAddPoups .popup-con .layui-form .layui-form-item .layui-form-label {
  width: auto;
  margin-right: 14px;
}
#calcAddPoups .popup-con h3 {
  color: #1D2129;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-right: 16px;
  height: 34px;
  line-height: 34px;
  width: 84px;
  margin-bottom: 18px;
}
#calcAddPoups .popup-con h3 em {
  font-size: 16px;
  color: #E23131;
  display: inline-block;
  margin-right: 5px;
}
#calcAddPoups .popup-con .table {
  margin-bottom: 20px;
}
#ruleAddPoups,
#gapaAddPoups {
  width: 840px;
}
#ruleAddPoups .popup-con .f-top,
#gapaAddPoups .popup-con .f-top {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
}
#ruleAddPoups .popup-con .f-top .add-lab,
#gapaAddPoups .popup-con .f-top .add-lab {
  width: auto;
  margin-bottom: 0;
  width: 60px;
  height: 28px;
}
#ruleAddPoups .popup-con .f-top .add-lab span,
#gapaAddPoups .popup-con .f-top .add-lab span {
  height: 28px;
  line-height: 28px;
}
#ruleAddPoups .popup-con .f-top h3,
#gapaAddPoups .popup-con .f-top h3 {
  margin-bottom: 0;
}
#ruleAddPoups .popup-con .layui-form,
#gapaAddPoups .popup-con .layui-form {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-items: flex-start;
}
#ruleAddPoups .popup-con .layui-form .layui-form-item,
#gapaAddPoups .popup-con .layui-form .layui-form-item {
  display: block;
  width: 240px;
  margin-right: 50px;
  flex-shrink: 0;
}
#ruleAddPoups .popup-con .layui-form .layui-form-item .layui-form-label,
#gapaAddPoups .popup-con .layui-form .layui-form-item .layui-form-label {
  width: auto;
  margin-right: 14px;
  float: none;
}
#ruleAddPoups .popup-con .layui-form .layui-form-item:last-child,
#gapaAddPoups .popup-con .layui-form .layui-form-item:last-child {
  width: 200px;
  margin-right: 0;
}
#ruleAddPoups .popup-con h3,
#gapaAddPoups .popup-con h3 {
  color: #1D2129;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-right: 16px;
  height: 34px;
  line-height: 34px;
  width: 84px;
  margin-bottom: 18px;
}
#ruleAddPoups .popup-con h3 em,
#gapaAddPoups .popup-con h3 em {
  font-size: 16px;
  color: #E23131;
  display: inline-block;
  margin-right: 5px;
}
#ruleAddPoups .popup-con .table,
#gapaAddPoups .popup-con .table {
  margin-bottom: 20px;
}
#ruleAddPoups .popup-con .table .layui-table-cell,
#gapaAddPoups .popup-con .table .layui-table-cell {
  overflow: visible;
  height: auto;
  line-height: normal;
}
#ruleAddPoups .popup-con .fractionalSegment,
#gapaAddPoups .popup-con .fractionalSegment {
  border: 1px solid #D4D6D9;
  border-radius: 4px;
  width: 240px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#ruleAddPoups .popup-con .fractionalSegment .sign,
#gapaAddPoups .popup-con .fractionalSegment .sign {
  background-color: #fff;
  width: 30px;
  height: 34px;
  flex-shrink: 0;
  text-align: center;
  line-height: 34px;
}
#ruleAddPoups .popup-con .fractionalSegment .sign.borderRa2,
#gapaAddPoups .popup-con .fractionalSegment .sign.borderRa2 {
  border-radius: 0 4px 4px 0;
}
#ruleAddPoups .popup-con .fractionalSegment .w64,
#gapaAddPoups .popup-con .fractionalSegment .w64 {
  width: 64px;
  border: none;
}
#ruleAddPoups .popup-con .fractionalSegment .w44,
#gapaAddPoups .popup-con .fractionalSegment .w44 {
  width: 44px;
  border: none;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-input.borderRa,
#gapaAddPoups .popup-con .fractionalSegment .layui-input.borderRa {
  border-radius: 4px 0 0 4px;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-input.borderRa1,
#gapaAddPoups .popup-con .fractionalSegment .layui-input.borderRa1 {
  border-radius: 0;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-input:hover,
#gapaAddPoups .popup-con .fractionalSegment .layui-input:hover {
  border: none !important;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-input:focus,
#gapaAddPoups .popup-con .fractionalSegment .layui-input:focus {
  border: none !important;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-form-selected .layui-input,
#gapaAddPoups .popup-con .fractionalSegment .layui-form-selected .layui-input {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input,
#gapaAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input {
  border-radius: 0;
  border-top: none;
  border-bottom: none;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input:hover,
#gapaAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input:hover {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input:focus,
#gapaAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input:focus {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
#ruleAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input:active,
#gapaAddPoups .popup-con .fractionalSegment .layui-select-title .layui-input:active {
  border: 1px solid #E5E6EB !important;
  border-top: none !important;
  border-bottom: none !important;
}
.scrollBox .layui-form-select dl {
  position: fixed;
  left: auto;
  top: auto;
  margin-top: 4px;
  width: 110px;
  min-width: auto;
}
.scrollBox .i-bottom .layui-form-select dl {
  width: 303px;
  min-width: auto;
}
.scrollBox .right .layui-form-select dl {
  width: 102px;
  min-width: auto;
}
.flex-area {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.flex-area .items {
  flex: 1;
}
.flex-area .items:first-child {
  margin-right: 45px;
}
.flex-area .items h4 {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: #131b26;
  margin-bottom: 15px;
}
.addPoups {
  width: 676px;
}
.addPoups .popup-con .queryContent {
  width: 100%;
  height: auto;
  background: #F0F6FF;
}
.addPoups .popup-con .queryContent ul li {
  border-bottom: 1px solid #D9E9FF;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.addPoups .popup-con .queryContent ul li .name {
  font-size: 14px;
  color: #8a8b99;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 40px;
  justify-content: center;
  flex: 1;
  flex-shrink: 0;
  border-right: 1px solid #D9E9FF;
}
.addPoups .popup-con .queryContent ul li .checkbox-list {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  flex-direction: row;
  flex-shrink: 0;
  padding: 5px 13px;
}
.addPoups .popup-con .queryContent ul li .checkbox-list span {
  padding-left: 27px;
  font-size: 14px;
  color: #8a8b99;
  background: url(../images/check-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
  height: 20px;
}
.addPoups .popup-con .queryContent ul li .checkbox-list span.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#addInput {
  width: 640px;
}
#achievementAddInput {
  width: 911px;
}
#achievementAddInput .popup-con .p-boxs .b-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 29px;
  margin-bottom: 30px;
}
#achievementAddInput .popup-con .p-boxs .b-top em {
  color: #ff4e4e;
  margin-right: 9px;
}
#achievementAddInput .popup-con .p-boxs .b-top .name {
  color: #131b26;
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
#achievementAddInput .popup-con .p-boxs .b-top .introIcon {
  position: relative;
}
#achievementAddInput .popup-con .p-boxs .b-top .introIcon:hover .bubble {
  display: block;
}
#achievementAddInput .popup-con .p-boxs .b-top .bubble {
  display: none;
  position: absolute;
  left: 100%;
  top: 50%;
  margin-top: -15px;
  padding-left: 17px;
}
#achievementAddInput .popup-con .p-boxs .b-top .bubble:after {
  content: '';
  position: absolute;
  left: 13px;
  top: 50%;
  width: 4px;
  height: 10px;
  margin-top: -5px;
  background: url(../images/triangle.png) no-repeat left center;
}
#achievementAddInput .popup-con .p-boxs .b-top .bubble span {
  display: block;
  border-radius: 4px;
  background: #F2F5F7;
  height: 29px;
  padding: 0 16px;
  font-size: 12px;
  line-height: 29px;
  color: #737B86;
  white-space: nowrap;
}
#achievementAddInput .popup-con .p-boxs .b-top .bubble span em {
  color: #3A8BFF;
  cursor: pointer;
  margin-left: 9px;
}
#achievementAddInput .popup-con .p-boxs .check-box {
  margin-bottom: 30px;
}
#achievementAddInput .popup-con .p-boxs .check-box ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
}
#achievementAddInput .popup-con .p-boxs .check-box ul li {
  padding-left: 28px;
  font-size: 14px;
  color: #474c59;
  margin-right: 30px;
  cursor: pointer;
  background: url(../images/check-icon.png) no-repeat left center;
}
#achievementAddInput .popup-con .p-boxs .check-box ul li.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#achievementAddInput .popup-con .p-boxs .level-interval {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
#achievementAddInput .popup-con .p-boxs .level-interval .name {
  width: 78px;
  flex-shrink: 0;
  line-height: 28px;
  color: #474c59;
  font-size: 14px;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box .lab {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 25px;
  margin-bottom: 20px;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box .lab .level {
  width: 74px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background: #FFF;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  text-align: center;
  line-height: 26px;
  margin-right: 10px;
  flex-shrink: 0;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box .lab .w84 {
  width: 84px;
  flex-shrink: 0;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box .lab .sign {
  color: #474c59;
  margin: 0 6px;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box .lab .delet {
  width: 20px;
  height: 20px;
  margin-left: 6px;
  background: url(../images/delete-icons.png) no-repeat center;
  cursor: pointer;
}
#achievementAddInput .popup-con .p-boxs .level-interval .li-box .lab span {
  border-radius: 4px;
  border: 1px solid #3A8BFF;
  width: 130px;
  height: 28px;
  text-align: center;
  line-height: 26px;
  cursor: pointer;
  font-size: 14px;
  padding-left: 34px;
  text-align: left;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  color: #3a8bff;
  background: url(../images/plus-icons.png) no-repeat 12px center;
}
