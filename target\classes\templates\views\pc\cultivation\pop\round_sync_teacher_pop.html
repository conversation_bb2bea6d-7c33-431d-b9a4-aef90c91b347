<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培养方案 - 轮次选择</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialog.css'}">
    <style>
        /* 优化后的样式 */
        .dialog .dialog-con {
            height: 220px;
            min-height: 180px; /* 添加最小高度 */
        }

        .j-search-con .j-select-year ul {
            max-height: 130px;
            scrollbar-width: thin; /* Firefox滚动条优化 */
            scrollbar-color: #C9CDD4 #F5F7FA;
        }

        /* Webkit滚动条优化 */
        .j-search-con .j-select-year ul::-webkit-scrollbar {
            width: 6px;
        }

        .j-search-con .j-select-year ul::-webkit-scrollbar-track {
            background: #F5F7FA;
            border-radius: 3px;
        }

        .j-search-con .j-select-year ul::-webkit-scrollbar-thumb {
            background: #C9CDD4;
            border-radius: 3px;
        }

        .j-search-con .j-select-year ul::-webkit-scrollbar-thumb:hover {
            background: #86909C;
        }

        /* 加载状态样式 */
        .loading-state {
            opacity: 0.6;
            pointer-events: none;
        }

        /* 错误提示样式 */
        .error-message {
            color: #f14848;
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }
    </style>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>
<body>
<div class="dialog" id="invigilateMax" style="width: 438px;" role="dialog" aria-labelledby="dialog-title" aria-modal="true">
    <div class="dialog-con">
        <div class="item">
            <div class="label" id="dialog-title">选择轮次</div>
            <div class="j-search-con single-box" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                <input type="text"
                       name="grade"
                       placeholder="请选择轮次"
                       readonly
                       class="schoolSel"
                       aria-label="选择轮次"
                       aria-describedby="round-error">
                <span class="j-arrow" aria-hidden="true"></span>
                <div class="j-select-year" role="listbox" aria-label="轮次选项">
                    <div class="search">
                        <input type="text"
                               placeholder="搜索轮次"
                               aria-label="搜索轮次"
                               autocomplete="off">
                        <span aria-hidden="true"></span>
                    </div>
                    <ul role="group">
                        <li role="option" data-value="round2" tabindex="0">第二轮</li>
                        <li role="option" data-value="round4" tabindex="0">第四轮</li>
                    </ul>
                </div>
                <div class="error-message" id="round-error" role="alert"></div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button type="button" class="pu-cancel" aria-label="取消操作">取消</button>
        <button type="button" class="pu-sure" id="invigilateSure" style="width: 88px;" aria-label="确定选择">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:inline="javascript">
    // 优化后的JavaScript代码
    (function() {
        'use strict';

        // 配置常量
        const CONFIG = {
            DOMAIN: "chaoxing.com",
            API_ENDPOINT: "/api/form/cultivation/topBtn/exportCultivationProcess",
            LOADING_TIMEOUT: 30000, // 30秒超时
            DEBOUNCE_DELAY: 300 // 搜索防抖延迟
        };

        // 全局变量（从Thymeleaf传入）
        /*[# th:if="${queryId}"]*/
        const queryId = /*[[${queryId}]]*/ '';
        const formId = /*[[${formId}]]*/ '';
        const fid = /*[[${fid}]]*/ '';
        const uid = /*[[${uid}]]*/ '';
        /*[/]*/

        document.domain = CONFIG.DOMAIN;

        // 工具函数
        const Utils = {
            // 防抖函数
            debounce: function(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },

            // 阻止事件冒泡
            stopBubble: function(e) {
                if (e && e.stopPropagation) {
                    e.stopPropagation();
                } else if (window.event) {
                    window.event.cancelBubble = true;
                }
            },

            // 显示错误信息
            showError: function(message) {
                const errorEl = $('#round-error');
                errorEl.text(message).show();
                setTimeout(() => errorEl.fadeOut(), 3000);
            },

            // 清除错误信息
            clearError: function() {
                $('#round-error').hide();
            },

            // 验证选择
            validateSelection: function() {
                const selectedValue = $('.schoolSel').val();
                if (!selectedValue || selectedValue === '请选择轮次') {
                    this.showError('请选择一个轮次');
                    return false;
                }
                this.clearError();
                return true;
            }
        };

        layui.use(['form', 'layer'], function () {
            const layer = layui.layer;

            // 轮次选择处理器
            const RoundSelector = {
                init: function() {
                    this.bindEvents();
                    this.initAccessibility();
                },

                bindEvents: function() {
                    // 确定按钮点击事件
                    $(".pu-sure").on('click', this.handleSubmit.bind(this));

                    // 选项选择事件
                    $(".j-search-con.single-box").on("click", ".j-select-year li", this.handleOptionSelect.bind(this));

                    // 搜索事件
                    $(".j-search-con").on('click', '.search input', Utils.stopBubble);
                    $(".j-search-con").on('input', '.search input', Utils.debounce(this.handleSearch.bind(this), CONFIG.DEBOUNCE_DELAY));

                    // 下拉框切换事件
                    $(".j-search-con").on("click", ".schoolSel", this.handleDropdownToggle.bind(this));

                    // 点击外部关闭下拉框
                    $(document).on("click", this.handleOutsideClick.bind(this));

                    // 键盘导航支持
                    this.bindKeyboardEvents();
                },

                initAccessibility: function() {
                    // 设置初始ARIA状态
                    $('.j-search-con.single-box').attr('aria-expanded', 'false');
                },

                bindKeyboardEvents: function() {
                    // ESC键关闭下拉框
                    $(document).on('keydown', (e) => {
                        if (e.key === 'Escape') {
                            this.closeDropdown();
                        }
                    });

                    // 回车键选择选项
                    $('.j-select-year li').on('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            $(e.target).click();
                        }
                    });
                },

                handleSubmit: function() {
                    if (!Utils.validateSelection()) {
                        return;
                    }

                    const $button = $(".pu-sure");
                    const selectedValue = $('.j-select-year li.active').attr('data-value');

                    // 设置加载状态
                    $button.addClass('loading-state').prop('disabled', true);
                    const loading = layer.load(1);

                    // 设置超时处理
                    const timeoutId = setTimeout(() => {
                        layer.close(loading);
                        $button.removeClass('loading-state').prop('disabled', false);
                        layer.msg('请求超时，请重试', {icon: 2, time: 3000});
                    }, CONFIG.LOADING_TIMEOUT);

                    $.post(CONFIG.API_ENDPOINT, {
                        queryId: queryId,
                        formId: formId,
                        fid: fid,
                        uid: uid,
                        templateId: selectedValue
                    })
                    .done((result) => {
                        clearTimeout(timeoutId);
                        if (result && result.success) {
                            this.sendMessageToParent({action: 1, success: true});
                        } else {
                            const message = result?.message || '操作失败，请重试';
                            layer.msg(message, {icon: 2, time: 3000});
                        }
                    })
                    .fail((xhr, status, error) => {
                        clearTimeout(timeoutId);
                        console.error('请求失败:', error);
                        layer.msg('网络错误，请检查网络连接', {icon: 2, time: 3000});
                    })
                    .always(() => {
                        layer.close(loading);
                        $button.removeClass('loading-state').prop('disabled', false);
                    });
                },

                handleOptionSelect: function(e) {
                    const $option = $(e.currentTarget);
                    const $container = $option.closest('.j-search-con');
                    const $input = $container.find('.schoolSel');

                    // 更新选中状态
                    $option.addClass('active').siblings().removeClass('active');

                    // 更新输入框值
                    $input.val($option.text());

                    // 关闭下拉框
                    this.closeDropdown($container);

                    // 清除错误信息
                    Utils.clearError();

                    Utils.stopBubble(e);
                },

                handleSearch: function(e) {
                    const $input = $(e.target);
                    const searchValue = $input.val().toLowerCase();
                    const $options = $input.closest('.j-search-con').find('ul li');

                    $options.each(function() {
                        const $option = $(this);
                        const text = $option.text().toLowerCase();

                        if (text.includes(searchValue)) {
                            $option.show();
                        } else {
                            $option.hide();
                        }
                    });

                    Utils.stopBubble(e);
                },

                handleDropdownToggle: function(e) {
                    const $container = $(e.target).closest('.j-search-con');
                    const isExpanded = $container.attr('aria-expanded') === 'true';

                    // 关闭其他下拉框
                    this.closeAllDropdowns();

                    if (!isExpanded) {
                        this.openDropdown($container);
                    }

                    Utils.stopBubble(e);
                },

                handleOutsideClick: function(event) {
                    const $target = $(event.target);
                    const $container = $target.closest('.j-search-con');

                    if ($container.length === 0) {
                        this.closeAllDropdowns();
                    }
                },

                openDropdown: function($container) {
                    $container.find('.j-arrow').addClass('j-arrow-slide');
                    $container.find('.j-select-year').addClass('slideShow');
                    $container.attr('aria-expanded', 'true');
                },

                closeDropdown: function($container) {
                    if ($container) {
                        $container.find('.j-arrow').removeClass('j-arrow-slide');
                        $container.find('.j-select-year').removeClass('slideShow');
                        $container.attr('aria-expanded', 'false');
                    }
                },

                closeAllDropdowns: function() {
                    $('.j-search-con').each((index, element) => {
                        this.closeDropdown($(element));
                    });
                },

                sendMessageToParent: function(data) {
                    try {
                        window.parent.postMessage(JSON.stringify(data), "*");
                    } catch (error) {
                        console.error('发送消息到父窗口失败:', error);
                    }
                }
            };

            // 取消按钮处理
            $(".pu-cancel").on('click', function() {
                RoundSelector.sendMessageToParent({action: 1, cancelled: true});
            });

            // 初始化
            RoundSelector.init();
        });

    })();
</script>
</html>