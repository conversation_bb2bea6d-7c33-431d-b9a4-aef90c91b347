$(function () {
    var form, table, laydate, layer;
    layui.use(['form', 'table', 'laydate', 'upload'],
      function () {
        var $ = layui.jquery;
        form = layui.form;
        table = layui.table;
        laydate = layui.laydate;
        layer = layui.layer;
        upload = layui.upload;

        form.on('switch(switch_filter)', function (data) {
            if (data.elem.checked) {
                $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item")
                    .addClass("showMore");
            } else {
                $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item")
                    .removeClass("showMore");
            }
        });


         //后移
  $(".layui-form").on("click", ".keyboard .k-con .delet", function () {
    let parentsEle = $(this).parents('.grade-keyboard');
    let conEle = parentsEle.prev().find('.f-con');
    let txt = conEle.find("span").last().text();
    let extraStr = conEle.find("span").last().attr("markId");
    conEle.find("span").last().remove();

    if (conEle.find("span").length == 0) {
        parentsEle.prev().find('.f-con').removeClass("cons");
    }

    if (extraStr) {
        $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item select")
            .each(function () {
                var kpl = $(this).attr("lay-filter");
                console.log(kpl);
                if (kpl == extraStr) {
                    $(this).val("");
                    $(this).attr("disabled", "disabled");
                    form.render('select');
                    if ($(this).parent().parent().hasClass("i-bottom")) {
                        $(this).parents(".item").find(".left .name")
                            .removeClass("cur");
                    } else {
                        $(this).parents("li").find(".name").removeClass(
                            "cur");
                    }


                }
            })


        $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .select select")
            .each(function () {
                var kpl = $(this).attr("lay-filter");
                console.log(kpl);
                if (kpl == extraStr) {
                    $(this).val("");
                    $(this).attr("disabled", "disabled");
                    form.render('select');
                    $(this).parents("li").find(".name").removeClass("cur");

                }
            })

    }
})
//清空
$(".layui-form").on("click", " .keyboard .k-con .empty", function () {
    let parentsEle = $(this).parents('.grade-keyboard');
    parentsEle.prev().find('.f-con span').remove();
    parentsEle.prev().find('.f-con').removeClass("cons");
    $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .name")
        .removeClass("cur");
    $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item select")
        .val('').attr("disabled", "disabled");

    $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .name")
        .removeClass("cur");
    $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li select")
        .val('').attr("disabled", "disabled");
    form.render('select');
})

//加减乘除
$(".layui-form").on("click", ".keyboard .k-con .sign", function () {
    let signTxt = $(this).text();
    let cls = $(this).attr("class");
    let parentsEle = $(this).parents('.grade-keyboard');
    parentsEle.prev().find('.f-con').append('<span class="' + cls + '">' +
        signTxt + '</span>');
    parentsEle.prev().find('.f-con').addClass("cons");
})

//输入数字
$(".layui-form").on("click", ".keyboard .k-con .num", function () {
    let signTxt = $(this).text();
    let cls = $(this).attr("class");
    let parentsEle = $(this).parents('.grade-keyboard');
    parentsEle.prev().find('.f-con').append('<span class="' + cls + '">' +
        signTxt + '</span>');
    parentsEle.prev().find('.f-con').addClass("cons");
})


//分项选中

$(".popup-con .grade-keyboard .score-breakdown").on("click",
    " .sb-cons .sb-list .item .i-con .right ul li .name",
    function () {
        $(this).toggleClass("cur");

        if (!$(this).hasClass("cur")) {
            $(this).parent().addClass("disabled");
            $(this).parent().find("select").attr("disabled", "disabled");
            form.render('select');
        } else {
            $(this).parent().removeClass("disabled");
            $(this).parent().find("select").removeAttr("disabled");
            form.render('select');
        }

    })

$("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown").on("click",
    ".sb-cons .sb-list ul li .name",
    function () {
        $(this).toggleClass("cur");
        if (!$(this).hasClass("cur")) {
            $(this).parent().addClass("disabled");
            $(this).parent().find("select").attr("disabled", "disabled");
            form.render('select');
        } else {
            $(this).parent().removeClass("disabled");
            $(this).parent().find("select").removeAttr("disabled");
            form.render('select');
        }
    })


$("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li")
    .each(function () {
        var layTitle = $(this).find("select").attr("lay-filter");
        console.log(layTitle);

        form.on('select(' + layTitle + ')', function (data) {
            var e = data.elem;
            var text = e[e.selectedIndex].text;
            var ptext = $(this).parents("li").find(".name").text();
            console.log(ptext);

            $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").addClass(
                "cons");
            $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").append(
                '<span markId="' + layTitle + '">' + ptext + " " +
                text + '</span>');
        })
    })





$("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li")
    .each(function () {
        var layTitle = $(this).find("select").attr("lay-filter");

        form.on('select(' + layTitle + ')', function (data) {
            //获取选中的文本
            var e = data.elem;
            var text = e[e.selectedIndex].text;
            var ptext = $(this).parents("li").find(".name").text();
            var ftext = $(this).parents(".item").find(".left .name").text();
            $("#gsbjsubitem .popup-con .lable .formula .f-con").addClass(
                "cons");
            $("#gsbjsubitem .popup-con .lable .formula .f-con").append(
                '<span markId="' + layTitle + '">' + ptext + " " + "(" +
                ftext + ")" + " " + text + '</span>');
        })
    })

$("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item")
    .each(function () {
        var layTitle = $(this).find(".i-bottom select").attr("lay-filter");
        form.on('select(' + layTitle + ')', function (data) {
            //获取选中的文本
            var e = data.elem;
            var text = e[e.selectedIndex].text;
            var ftext = $(this).parents(".item").find(".left .name").text();
            $("#gsbjsubitem .popup-con .lable .formula .f-con").addClass(
                "cons");
            $("#gsbjsubitem .popup-con .lable .formula .f-con").append(
                '<span markId="' + layTitle + '">' + ftext + " " +
                text + '</span>');
        })
    })

//以及分项选中

$("#gsbjsubitem .popup-con .grade-keyboard").on("click",
    ".score-breakdown .sb-cons .sb-list .item .i-con .left .name",
    function () {
        $(this).toggleClass("cur");
        if (!$(this).hasClass("cur")) {
            $(this).parents(".item").find(".i-bottom select").attr("disabled",
                "disabled");
            form.render('select');
        } else {
            $(this).parents(".item").find(".i-bottom select").removeAttr(
            "disabled");
            form.render('select');
        }
    })

//select 点击事件
$("#gsbjsubitem").on("click", ".layui-form-select .layui-select-title", function (e) {
    console.log($(e.target));
    let objll = $(this).offset().left;
    let objtt = $(this).offset().top + 34;
    console.log(objll);
    console.log(objtt);
    $(this).parent().find(".layui-anim-upbit").css({
        'left': objll,
        'top': objtt
    });
})

$(".scrollBox").mousedown(function (e) {
    var _con = $('.select');
    if (!_con.is(event.target) && _con.has(event.target).length === 0) {
        $(".layui-form-select").removeClass("layui-form-selected")
    }
});



   //select 点击事件
   $("#gsbjsubitemTwo").on("click", ".layui-form-select .layui-select-title", function (e) {
            console.log($(e.target));
            let objll = $(this).offset().left;
            let objtt = $(this).offset().top + 34;
            console.log(objll);
            console.log(objtt);
            $(this).parent().find(".layui-anim-upbit").css({
                'left': objll,
                'top': objtt
            });
        })



      })
    })
  
  
  