package com.chaoxing.academic.controller.pc.cultivation;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.chaoxing.academic.annotation.NeedLogin;
import com.chaoxing.academic.api.form.FormApi;
import com.chaoxing.academic.api.passport.UserApi;
import com.chaoxing.academic.controller.BaseController;
import com.chaoxing.academic.design.template.bo.FormTopBtnBO;
import com.chaoxing.academic.entity.dto.cultivation.CourseInformationDataDTO;
import com.chaoxing.academic.entity.dto.cultivation.TrainingProgramDataDTO;
import com.chaoxing.academic.entity.form.basic.AcademicYearSemesterForm;
import com.chaoxing.academic.entity.form.cultivation.CourseAllocationResultForm;
import com.chaoxing.academic.entity.form.cultivation.OpenCourseRecordForm;
import com.chaoxing.academic.entity.po.timetable.Timetable;
import com.chaoxing.academic.entity.po.timetable.TimetableLesson;
import com.chaoxing.academic.entity.po.user.User;
import com.chaoxing.academic.entity.vo.R;
import com.chaoxing.academic.entity.vo.form.FormRightBtnVO;
import com.chaoxing.academic.entity.vo.form.FormTopBtnVO;
import com.chaoxing.academic.entity.vo.form.TopReturn;
import com.chaoxing.academic.service.cultivation.CultivationPopService;
import com.chaoxing.academic.service.cultivation.MajorCourseSetService;
import com.chaoxing.academic.service.cultivation.excel.CourseInformationReadListener;
import com.chaoxing.academic.service.cultivation.excel.TrainingProgramReadListener;
import com.chaoxing.academic.service.timetable.TimetableLessonService;
import com.chaoxing.academic.service.timetable.TimetableService;
import com.chaoxing.academic.utils.MyUtils;
import com.chaoxing.academic.utils.StringUtils;
import com.chaoxing.academic.utils.UserUtils;
import com.chaoxing.academic.utils.date.DateUtils;
import com.chaoxing.academic.utils.form.basic.SemesterUtils;
import com.chaoxing.academic.utils.redis.RedisUtils;
import com.chaoxing.academic.utils.security.MD5;
import com.chaoxing.academic.utils.task.thread.ThreadTaskUtils;
import com.chaoxing.office.app.entity.forms.dto.data.FormsData;
import com.chaoxing.office.app.entity.forms.dto.data.field.SelectBoxField;
import com.chaoxing.office.app.service.api.OfficeFormsApiInvokeService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@NeedLogin
@Slf4j
@RequestMapping("/cultivation")
public class CultivationPopController extends BaseController {

    @Autowired
    CultivationPopService cultivationPopService;

    @Autowired
    TimetableService timetableService;

    @Autowired
    MajorCourseSetService majorCourseSetService;

    @Resource
    TimetableLessonService timetableLessonService;

    /**
     * 公开课记录弹窗
     *
     * @return
     */
    @RequestMapping("openCourseRecordPop")
    public ModelAndView openCourseRecordPop(FormTopBtnBO formTopBtnBO) {
        Map<String, Object> modelMap = new HashMap<>();
        List<AcademicYearSemesterForm> all = SemesterUtils.all(formTopBtnBO.getFid());
        String curTerm = SemesterUtils.currentNum(formTopBtnBO.getFid());
        modelMap.put("curTerm", curTerm);
        modelMap.put("list", all);
        return newView("/views/pc/cultivation/pop/open_course_record_pop", modelMap);
    }


    /**
     * 教师弹窗
     *
     * @param formTopBtnBO
     * @return
     */
    @RequestMapping("topTeacherPop")
    public ModelAndView topTeacherPop(FormTopBtnBO formTopBtnBO, String formField) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnBO", formTopBtnBO);
        modelMap.put("formField", formField);
        modelMap.put("semester", SemesterUtils.currentNum(formTopBtnBO.getFid()));
        return newView("/views/pc/cultivation/pop/top_teacher_pop", modelMap);
    }

    /**
     * 学期学分汇总弹窗
     *
     * @param formUserId
     * @return
     */
    @RequestMapping("termCreditSummaryPop")
    public ModelAndView termCreditSummaryPop(FormRightBtnVO formRightBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formRightBtnVO", formRightBtnVO);
        return newView("/views/pc/cultivation/pop/term_credit_summary_pop", modelMap);
    }

    /**
     * 批量编辑开课管理
     *
     * @param formTopBtnBO
     * @return
     */
    @RequestMapping("batchEditPop")
    public ModelAndView batchEditPop(FormTopBtnBO formTopBtnBO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnBO", formTopBtnBO);
        return newView("/views/pc/cultivation/pop/batch_edit_pop", modelMap);
    }

    /**
     * 教室弹窗
     *
     * @param formTopBtnBO
     * @return
     */
    @RequestMapping("topClassRoomPop")
    public ModelAndView topClassRoomPop(FormTopBtnBO formTopBtnBO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnBO", formTopBtnBO);
        return newView("/views/pc/cultivation/pop/top_class_room_pop", modelMap);
    }

    /**
     * 周课时统计弹窗
     *
     * @param formTopBtnBO
     * @return
     */
    @RequestMapping("weekHourStatisticsPop")
    public ModelAndView weekHourStatisticsPop(FormTopBtnBO formTopBtnBO, String flowId, String enc, String operateUid) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnBO", formTopBtnBO);
        modelMap.put("flowId", flowId);
        modelMap.put("enc", enc);
        modelMap.put("operateUid", operateUid);
        return newView("/views/pc/cultivation/pop/week_hour_statistics_pop", modelMap);
    }

    /**
     * 表单顶部按钮执行前置校验
     *
     * @param formTopBtnBO
     * @param executeUrl
     * @return
     */
    @RequestMapping("executePreCheckPop")
    public ModelAndView executePreCheckPop(FormTopBtnBO formTopBtnBO, String executeUrl) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnBO", formTopBtnBO);
        modelMap.put("executeUrl", executeUrl);
        return newView("/views/pc/cultivation/pop/execute_pre_check_pop", modelMap);
    }

    /**
     * 指定教材弹窗
     *
     * @param deptId
     * @return
     */
    @RequestMapping("materialPop")
    public ModelAndView materialPop(Integer deptId) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("deptId", deptId);
        return newView("/views/pc/cultivation/pop/material_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @param type         开班类型
     * @description 开班弹窗
     * <AUTHOR>
     * @date 2024/5/27 14:05
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("openClazzPop")
    public ModelAndView openClazzPop(FormTopBtnVO formTopBtnVO, Integer type) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        modelMap.put("type", type);
        return newView("/views/pc/cultivation/pop/open_clazz_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 批量设置联排节次弹窗
     * <AUTHOR>
     * @date 2024/7/19 11:11
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("jointSchedulePop")
    public ModelAndView jointSchedulePop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/joint_schedule_pop", modelMap);
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @description 开课总学时统计弹窗
     * <AUTHOR>
     * @date 2024/7/25 9:20
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("totalHoursStatisticsPop")
    public ModelAndView totalHoursStatisticsPop(FormRightBtnVO formRightBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formRightBtnVO", formRightBtnVO);
        return newView("/views/pc/cultivation/pop/total_hours_statistics_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 培养方案打回学年学期弹窗
     * <AUTHOR>
     * @date 2024/8/15 19:04
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("repulseTrainingPlanPop")
    public ModelAndView repulseTrainingPlanPop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/repulse_training_plan_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 批量导入开课信息弹窗
     * <AUTHOR>
     * @date 2024/9/4 14:48
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("importCourseInformationPop")
    public ModelAndView importCourseInformationPop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/import_course_information_pop", modelMap);
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @param operateType    1 修改教学计划 2 停用教学计划
     * @description 教学计划变更弹窗
     * <AUTHOR>
     * @date 2024/10/23 15:19
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("teachPlanChangePop")
    public ModelAndView teachPlanChangePop(FormRightBtnVO formRightBtnVO, Integer operateType) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formRightBtnVO", formRightBtnVO);
        modelMap.put("operateType", operateType);
        return newView("/views/pc/cultivation/pop/teach_plan_change_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 批量导入培养方案弹窗
     * <AUTHOR>
     * @date 2024/11/4 19:24
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("importTrainingProgramPop")
    public ModelAndView importTrainingProgramPop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/import_training_program_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 打回开课弹窗
     * <AUTHOR>
     * @date 2024/11/5 10:23
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("repulseCourseManagePop")
    public ModelAndView repulseCourseManagePop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/repulse_course_manage_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 培养方案打印模板弹窗
     * <AUTHOR>
     * @date 2024/12/5 16:25
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("trainingProgramTplPop")
    public ModelAndView trainingProgramTplPop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/training_program_tpl_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 开课信息表配课弹窗
     * <AUTHOR>
     * @date 2024/12/5 16:25
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("courseAllocationPop")
    public ModelAndView courseAllocationPop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        return newView("/views/pc/cultivation/pop/course_allocation_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 陕西省机械高级技工学校教学任务书弹窗
     * <AUTHOR>
     * @date 2025/6/19 14:28
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("teachTaskBook19489Pop")
    public ModelAndView teachTaskBook19489Pop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        modelMap.put("enc", MD5.encrypt(formTopBtnVO.getQueryId() + "#" + formTopBtnVO.getFid()
                + "#" + formTopBtnVO.getUid() + "#" + DateUtils.yyyyMMddHH()));
        return newView("/views/pc/cultivation/pop/teach_task_book_19489_pop", modelMap);
    }

    /**
     * @param formUserId 专业教学计划数据id
     * @description 删除
     * <AUTHOR>
     * @date 2025/6/24 19:24
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("deleteMajorTeachPlanPop")
    public ModelAndView deleteMajorTeachPlanPop(String formUserId) {
        Map<String, Object> modelMap = new HashMap<>();
        Boolean flag = cultivationPopService.deleteMajorTeachPlanValidate(formUserId);
        modelMap.put("flag", flag);
        modelMap.put("formUserId", formUserId);
        return newView("/views/pc/cultivation/pop/delete_maintain_teach_plan_pop", modelMap);
    }

    /**
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 银川职业技术学院教学任务书弹窗
     * <AUTHOR>
     * @date 2025/6/30 8:26
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("teachTaskBook112009Pop")
    public ModelAndView teachTaskBook112009Pop(FormTopBtnVO formTopBtnVO) {
        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("formTopBtnVO", formTopBtnVO);
        modelMap.put("enc", MD5.encrypt(formTopBtnVO.getQueryId() + "#" + formTopBtnVO.getFid()
                + "#" + formTopBtnVO.getUid() + "#" + DateUtils.yyyyMMddHH()));
        return newView("/views/pc/cultivation/pop/teach_task_book_112009_pop", modelMap);
    }

    /**
     * @description 天津机电根据轮次同步授课教师
     * <AUTHOR>
     * @date 2025/8/5 8:19
     * @retrun org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("roundSyncTeacherPop")
    public ModelAndView roundSyncTeacherPop() {
        return newView("/views/pc/cultivation/pop/round_sync_teacher_pop");
    }

    /**
     * 获取公开课记录数据
     *
     * @return
     */
    @RequestMapping("getOpenCourseRecordData")
    public R<JSONObject> getOpenCourseRecordData(Integer week, String term) {
        User user = UserUtils.user();
        JSONObject json = new JSONObject();
        if (week == null) {
            return R.fail("参数异常");
        }
        if (user == null) {
            return R.fail("获取用户信息异常");
        }
        Timetable timetable = timetableService.inuseTimetable(user.getFid());
        if (timetable == null) {
            return R.fail("获取课表信息异常");
        }
        JSONObject schoolInfo = UserApi.getSchoolInfo(Math.toIntExact(user.getFid()));
        String schoolName = MyUtils.isNotEmpty(schoolInfo) && schoolInfo.containsKey("name") ? schoolInfo.getString("name") : "";
        List<OpenCourseRecordForm> list = cultivationPopService.getFormData(user.getFid(), week, term);
        List<TimetableLesson> timetableLessons = timetableLessonService.firstDayLessons(timetable);
        json.put("list", list);
        json.put("timetable", timetable);
        json.put("timetableLessons", timetableLessons);
        json.put("schoolName", schoolName);
        json.put("term", term);
        return R.success(json);
    }

    /**
     * 导出公开课记录数据
     *
     * @param weekly
     * @return
     */
    @RequestMapping("exportOpenCourseRecordData")
    public R<JSONObject> exportOpenCourseRecordData(Integer weekly, Integer type, String term) {
        if (weekly == null) {
            return R.fail("参数异常");
        }
        return cultivationPopService.exportDataToExcel(weekly, type, term);
    }

    /**
     * 导出开课信息表数据
     *
     * @param fid
     * @param semester
     * @param dataType
     * @return
     */
    @RequestMapping("exportCourseInformationData")
    public R<JSONObject> exportCourseInformationData(Integer fid, String semester, Integer dataType) {
        if (fid == null || StringUtils.isBlank(semester) || dataType == null) {
            return R.fail("参数异常");
        }
        return cultivationPopService.exportCourseDataToExcel(fid, semester, dataType);
    }

    /**
     * 开课信息表指定教师
     *
     * @param formTopBtnBO
     * @return
     */
    @RequestMapping("appointTeacher")
    public TopReturn appointTeacher(FormTopBtnBO formTopBtnBO, String formUserId, String formField, String formFieldVal, Integer syncScoreTeacher) {
        if (ObjectUtil.hasEmpty(formTopBtnBO.getUid(), formTopBtnBO.getFid(), formTopBtnBO.getQueryId())) {
            return TopReturn.fail("参数异常");
        }
        ThreadTaskUtils.run(() -> cultivationPopService.appointTeacher(formTopBtnBO, formUserId, formField, formFieldVal, syncScoreTeacher));
        return TopReturn.success();
    }

    /**
     * 获取教研室数据
     *
     * @return
     */
    @RequestMapping("getSubjectGroupData")
    public R getSubjectGroupData(Integer fid) {
        return cultivationPopService.getSubjectGroupData(fid);
    }


    /**
     * 学期学分汇总
     *
     * @param formUserId
     * @return
     */
    @RequestMapping("termCreditSummary")
    public R termCreditSummary(FormRightBtnVO formRightBtnVO) {
        return cultivationPopService.termCreditSummary(formRightBtnVO);
    }

    /**
     * 数据工厂统计
     *
     * @param deptId
     * @param uid
     * @param flowId
     * @param enc
     * @return
     */
    @RequestMapping("formFlowData")
    public R formFlowData(Integer deptId, Integer uid, String flowId, String enc) {
        return R.success(FormApi.formFlowData(deptId, uid, flowId, enc));
    }

    /**
     * 数据工厂统计状态
     *
     * @param deptId
     * @param uid
     * @param flowId
     * @param enc
     * @return
     */
    @RequestMapping("formFlowStatus")
    public R formFlowStatus(Integer deptId, Integer uid, String flowId, String enc) {
        return R.success(FormApi.formFlowStatus(deptId, uid, flowId, enc));
    }

    /**
     * 更新表单指定字段值
     */
    @RequestMapping("updFormFieldVal")
    @SneakyThrows
    public R updFormFieldVal(String formAlias, String formUserIds, String fieldAlias, String fieldVal, Integer fid, Integer uid) {
        if (ObjectUtil.hasEmpty(fieldVal, fieldAlias, fid, formUserIds, formAlias, uid)) {
            return R.fail("参数异常");
        }
        String[] formUserIdArray = formUserIds.split(",");
        for (String fromUserId : formUserIdArray) {
            FormsData formsData = new FormsData();
            String[] fieldAliasArray = fieldAlias.split(",");
            String[] fieldValArray = fieldVal.split(",");
            for (int i = 0; i < fieldAliasArray.length; i++) {
                formsData.addField(new SelectBoxField(fieldAliasArray[i], fieldValArray[i]));
            }
            OfficeFormsApiInvokeService.updateCustomFormsDataByAlias(fid, formAlias, Long.valueOf(fromUserId), null, false, 1, Long.valueOf(uid), formsData);
        }
        return R.success();
    }

    /**
     * @param semester  学年学期
     * @param teacherNo 教师工号
     * @param fid       单位id
     * @description 获取教师周课时
     * <AUTHOR>
     * @date 2024/6/4 15:54
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @PostMapping("getTeacherWeekHour")
    public R getTeacherWeekHour(String semester, String teacherNo, Integer fid) {
        return cultivationPopService.getTeacherWeekHour(semester, teacherNo, fid);
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @description 获取开课总学时统计数据
     * <AUTHOR>
     * @date 2024/7/25 9:39
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @PostMapping("getTotalHoursStatisticsData")
    public R getTotalHoursStatisticsData(FormRightBtnVO formRightBtnVO) {
        return cultivationPopService.totalHoursStatisticsData(formRightBtnVO);
    }

    /**
     * @param file         导入文件
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 批量导入开课信息数据处理
     * <AUTHOR>
     * @date 2024/9/4 14:53
     * @retrun void
     **/
    @PostMapping("importCourseInformationHandler")
    public R importCourseInformationHandler(MultipartFile file, FormTopBtnVO formTopBtnVO) {
        try {
            Integer uid = formTopBtnVO.getUid();
            Integer fid = formTopBtnVO.getFid();
            String successKey = "course_information_success_record_" + uid + "_" + fid;
            String failedKey = "course_information_fail_record_" + uid + "_" + fid;
            String totalKey = "course_information_total_record_" + uid + "_" + fid;
            RedisUtils.del(successKey, failedKey, totalKey);
            EasyExcel.read(file.getInputStream(),
                    CourseInformationDataDTO.class,
                    new CourseInformationReadListener(formTopBtnVO.getFid(), formTopBtnVO.getUid())).sheet().doRead();
        } catch (Exception e) {
            log.error("导入开课信息数据异常", e);
            return R.fail();
        }
        return R.success();
    }

    /**
     * @param successKey 成功条数
     * @param failedKey  失败条数
     * @param totalKey   总条数
     * @description 获取导入进度
     * <AUTHOR>
     * @date 2024/9/4 16:32
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    @PostMapping("getImportProcess")
    public JSONObject getImportProcess(String successKey, String failedKey, String totalKey) {
        int successCount = RedisUtils.exists(successKey) ? RedisUtils.integer(successKey) : 0;
        int failCount = RedisUtils.exists(failedKey) ? RedisUtils.integer(failedKey) : 0;
        int totalCount = RedisUtils.exists(totalKey) ? RedisUtils.integer(totalKey) : 0;
        return new JSONObject().fluentPut("successCount", successCount).fluentPut("failCount", failCount).fluentPut("totalCount", totalCount);
    }

    /**
     * @param fid      单位id
     * @param fileName 模板名称
     * @description 下载导入模板
     * <AUTHOR>
     * @date 2024/9/4 20:22
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @PostMapping("downImportTpl")
    public R downImportTpl(Integer fid, String fileName) {
        return cultivationPopService.downImportTpl(fid, fileName);
    }

    /**
     * @param filePath 文件地址
     * @description 获取上次错误数据文件
     * <AUTHOR>
     * @date 2024/9/5 19:34
     * @retrun java.lang.Boolean
     **/
    @PostMapping("getErrorDataFile")
    public Boolean getErrorDataFile(String filePath) {
        return FileUtil.exist(filePath);
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @param operateType    1 修改教学计划 2 停用教学计划
     * @description 教学计划变更操作校验
     * <AUTHOR>
     * @date 2024/10/23 15:24
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @PostMapping("teachPlanChangeOperateVerify")
    public R teachPlanChangeOperateVerify(FormRightBtnVO formRightBtnVO, Integer operateType) {
        return cultivationPopService.teachPlanChangeOperateVerify(formRightBtnVO, operateType);
    }

    /**
     * @param file         导入文件
     * @param formTopBtnVO 顶部按钮参数对象
     * @description 批量导入培养方案数据处理
     * <AUTHOR>
     * @date 2024/9/4 14:53
     * @retrun void
     **/
    @PostMapping("importTrainingProgramHandler")
    public R importTrainingProgramHandler(MultipartFile file, FormTopBtnVO formTopBtnVO) {
        try {
            Integer uid = formTopBtnVO.getUid();
            Integer fid = formTopBtnVO.getFid();
            String successKey = "training_program_success_record_" + uid + "_" + fid;
            String failedKey = "training_program_fail_record_" + uid + "_" + fid;
            String totalKey = "training_program_total_record_" + uid + "_" + fid;
            RedisUtils.del(successKey, failedKey, totalKey);
            EasyExcel.read(file.getInputStream(),
                            TrainingProgramDataDTO.class,
                            new TrainingProgramReadListener(formTopBtnVO.getFid(), formTopBtnVO.getUid(), majorCourseSetService)).sheet()
                    .headRowNumber(4)
                    .doRead();
        } catch (Exception e) {
            log.error("导入培养方案数据异常", e);
            return R.fail();
        }
        return R.success();
    }

    /**
     * @param major 专业
     * @param grade 年级
     * @param fid   单位id
     * @description 根据专业和年级获取班级列表
     * <AUTHOR>
     * @date 2024/11/13 16:20
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @PostMapping("getClassByMajorAndGrade")
    public R getClassByMajorAndGrade(String major, String grade, Integer fid) {
        return cultivationPopService.getClassByMajorAndGrade(major, grade, fid);
    }

    /**
     * @param fid    单位id
     * @param formId 表单id
     * @description 获取培养方案打印模板
     * <AUTHOR>
     * @date 2024/12/5 16:30
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    @PostMapping("getTrainingProgramTpl")
    public JSONObject getTrainingProgramTpl(Integer fid, Integer formId) {
        return cultivationPopService.getTrainingProgramTpl(fid, formId);
    }

    /**
     * @param info  筛选条件
     * @param page  当前页
     * @param limit 每页显示条数
     * @description 获取配课数据
     * <AUTHOR>
     * @date 2024/12/5 16:30
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    @RequestMapping("getCourseAllocationData")
    public R getCourseAllocationData(CourseAllocationResultForm info, int page, int limit) {
        return cultivationPopService.getCourseAllocationData(info, page, limit);
    }

    /**
     * @param formUserId 专业教学计划数据id
     * @description 删除专业教学计划
     * <AUTHOR>
     * @date 2025/6/24 20:15
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @RequestMapping("deleteMajorTeachPlan")
    public R deleteMajorTeachPlan(String formUserId) {
        return cultivationPopService.deleteMajorTeachPlan(formUserId);
    }

    /**
     * @param round 轮次
     * @description 天津机电根据轮次同步授课教师
     * <AUTHOR>
     * @date 2025/8/5 8:28
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @RequestMapping("roundSyncTeacher")
    public R roundSyncTeacher(String round) {
        return cultivationPopService.roundSyncTeacher(round);
    }
}

