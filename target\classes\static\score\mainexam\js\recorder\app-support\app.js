/*
录音
https://github.com/xiangyuecn/Recorder
src: app-support/app.js,app-support/app-ios-weixin-support.js,app-support/app-native-support.js
*/
!function(u){"use strict";var n=/MicroMessenger/i.test(navigator.userAgent),e=u.RecordAppBaseFolder||"/Recorder/dist/",t=u.OnRecordAppInstalled,f=[{Key:"Native",Support:function(e){m.AlwaysAppUseJS?e(!1):r.Config.IsApp(e)},CanProcess:function(){return!0},Config:{IsApp:function(e){e(!1)},JsBridgeRequestPermission:function(e,t){t("JsBridgeRequestPermission未实现")},JsBridgeStart:function(e,t,n){n("JsBridgeStart未实现")},JsBridgeStop:function(e,t){t("JsBridgeStop未实现")},paths:[{url:e+"app-support/app-native-support.js",check:function(){return!r.IsInit}}]},ExtendDefault:!0},{Key:"IOS-Weixin",Support:function(t){m.AlwaysUseWeixinJS||!Recorder.Support()?o.Config.Enable(function(e){t(!!e&&n)}):t(!1)},CanProcess:function(){return!1},Config:{Enable:function(e){e(!0)},WxReady:function(e){e(null,"未实现IOS-Weixin.Config.WxReady")},DownWxMedia:function(e,t,n){n("下载素材接口DownWxMedia未实现")},paths:[{url:e+"app-support/app-ios-weixin-support.js",check:function(){return!o.IsInit}},{url:e+"engine/beta-amr.js",lazyBeforeStop:1,check:function(){return!Recorder.prototype.amr}}]},ExtendDefault:!0},{Key:"Default",Support:function(e){e(!0)},CanProcess:function(){return!0},Config:{paths:[{url:e+"recorder-core.js",check:function(){return!u.Recorder}},{url:e+"engine/mp3.js",lazyBeforeStart:1,check:function(){return!Recorder.prototype.mp3}}]}}],r=f[0],o=f[1],v=f[2];v.RequestPermission=function(e,t){var n=m.__Rec;n&&(n.close(),m.__Rec=null);var r=Recorder();r.open(function(){e()},t)},v.Start=function(e,t,n){var r=m.__Rec;null!=r&&r.close(),m.__Rec=r=Recorder(e),r.appSet=e,r.open(function(){r.start(),t()},function(e){n(e)})},v.Stop=function(n,t){var r=m.__Rec;if(!r)return Recorder.IsOpen()&&((r=Recorder()).open(),r.close()),void t("未开始录音");var o=function(){for(var e in r.close(),r.set)r.appSet[e]=r.set[e]},e=function(e){o(),t(e),m.__Rec=null};n?r.stop(function(e,t){o(),m._SRec=r,n(e,t),m.__Rec=null},e):e("仅清理资源")};var R=function(e,t){var n=new Date,r=("0"+n.getMinutes()).substr(-2)+":"+("0"+n.getSeconds()).substr(-2)+"."+("00"+n.getMilliseconds()).substr(-3),o=["["+r+" RecordApp]["+(m.Current&&m.Current.Key||"?")+"]"+e],a=arguments,i=u.console||{},s=2,c=i.log;for("number"==typeof t?c=1==t?i.error:3==t?i.warn:c:s=1;s<a.length;s++)o.push(a[s]);l?c&&c("[IsLoser]"+o[0],1<o.length?o:""):c.apply(i,o)},l=!0;try{l=!console.log.apply}catch(e){}var m={LM:"2022-03-03 18:58:07",Current:0,CLog:R,IsWx:n,BaseFolder:e,UseLazyLoad:!0,AlwaysUseWeixinJS:!1,AlwaysAppUseJS:!1,Platforms:{Native:r,Weixin:o,Default:v},Js:function(o,a,i,e){var s=(e=e||u).document,c=function(e){if(e>=o.length)a();else{var t=o[e],n=t.url;if(!1!==t.check()){var r=s.createElement("script");r.setAttribute("type","text/javascript"),r.setAttribute("src",n),r.onload=function(){c(e+1)},r.onerror=function(e){i("请求失败:"+(e.message||"-")+"，"+n)},s.body.appendChild(r)}else c(e+1)}};c(0)},Install:function(n,r){var o=m.__reqs||(m.__reqs=[]);o.push({s:n,f:r}),n=function(){a("s",arguments)},r=function(e,t){a("f",arguments)};var a=function(e,t){for(var n=0;n<o.length;n++)o[n][e].apply(null,t);o.length=0};if(!(1<o.length)){var p=function(e,t,n){for(var r,o=e.Config,a=o.paths,i=[],s=0;s<a.length;s++)r=a[s],m.UseLazyLoad?r.lazyBeforeStart?t&&t.push(r):r.lazyBeforeStop?n&&n.push(r):i.push(r):i.push(r);return i},i=0,s=function(t,e){if(t.IsInit)e();else{var n=p(t);R("Install "+t.Key+"...",n),m.Js(n,function(){t.IsInit=!0,e()},function(e){R(e="初始化js库失败："+e,t),r(e)})}},d=function(n,r){var e=[],t=[];p(n,e,t),n.ExtendDefault&&p(v,e,t);var o=n.LazyReady;o||(o=n.LazyReady={fsta:[],fsto:[],fall:[],usta:0,usto:0,msta:"",msto:""},n.LazyAtStart=function(e){a()?e(o.msta):o.fsta.push(e)},n.LazyAtStop=function(e){i()?e(o.msto):o.fsto.push(e)},n.OnLazyReady=function(e){a()&&i()?e(o.msta||o.msto):o.fall.push(e)});var a=function(){return 1==o.usta||3==o.usta},i=function(){return 1==o.usto||3==o.usto},s=r?"usta":"usto",c=r?"msta":"msto",u=r?"fsta":"fsto",f=function(e){e&&R("Lazy Load:"+s);var t=function(e,t){var n=o[e];o[e]=[];for(var r=0;r<n.length;r++)n[r](t)};2!=o[s]&&t(u,o[c]),a()&&i()&&t("fall",o.msta||o.msto),r&&d(n)};if(o[s]<2){o[c]="",o[s]=2;var l=r?e:t;R("Lazy Load..."+s,l),m.Js(l,function(){o[s]=3,f(1)},function(e){R(e="加载js库失败["+s+"]："+e,n),o[s]=1,o[c]=e,f(1)})}else f()},c=function(t){if(t)d(t,1),m.Current=t,R("Install Success"),n();else{var e=function(){t.Support(function(e){e?s(t,function(){c(t)}):(i++,c())})};(t=f[i]).ExtendDefault?s(v,e):e()}};c(m.Current)}},GetStartUsedRecOrNull:function(){return m.__Rec||null},GetStopUsedRec:function(){return m._SRec||null},RequestPermission:function(t,n){var r=function(e,t){R("录音权限请求失败："+e+",isUserNotAllow:"+(t=!!t),1),n&&n(e,t)};R("RequestPermission..."),m.Install(function(){var e=m.Current;R("开始请求录音权限"),e.RequestPermission(function(){R("录音权限请求成功"),t&&t()},r)},function(e){r("Install失败："+e)})},Start:function(t,n,r){var o=function(e){R("开始录音失败："+e,1),r&&r(e)};R("Start...");var a=m.Current;if(a){t||(t={});var e={type:"mp3",sampleRate:16e3,bitRate:16,onProcess:function(){}};for(var i in e)t[i]||(t[i]=e[i]);var s=Recorder(t),c=s.envCheck({envName:a.Key,canProcess:a.CanProcess()});if(c)o("不能录音："+c);else{var u=m._SRec=0;a.LazyAtStart(function(e){u&&R("Start Wait Ready "+(Date.now()-u)+"ms",3),u=1,e?o(e):a.Start(t,function(){R("开始录音",t),n&&n()},o)}),u||R("Start Wait Ready...",3),u=Date.now()}}else o("需先调用RequestPermission")},Stop:function(n,t){var r=function(e){R("结束录音失败："+e,1),m._SRec=0,t&&t(e)};R("Stop...");var o=Date.now(),a=m.Current;if(a){var i=0;a.LazyAtStop(function(e){i&&R("Stop Wait Ready "+(Date.now()-i)+"ms",3),i=1,e?r(e):a.Stop(n?function(e,t){R("结束录音 耗时"+(Date.now()-o)+"ms 音频"+t+"ms/"+e.size+"b"),n(e,t),m._SRec=0}:null,r)}),i||R("Stop Wait Ready...",3),i=Date.now()}else r("需先调用RequestPermission")}};u.RecordApp=m,R("【提醒】因为iOS 14.3+已无需本兼容方案即可实现H5录音，所以RecordApp正逐渐失去存在的意义；如果你不打算兼容老版本iOS，请直接使用简单强大的Recorder H5即可。",3),t&&t()}(window),"function"==typeof define&&define.amd&&define(function(){return RecordApp}),"object"==typeof module&&module.exports&&(module.exports=RecordApp),function(){"use strict";var w=RecordApp,y=w.CLog,s=w.Platforms.Weixin,d=s.Config;s.IsInit=!0;var o,c={};s.RequestPermission=function(e,t){if(5!=o)if(a.push({t:e,f:t}),1!=o){o=1;var r=function(e,t){o=e?0:5;var n=a;a=[];for(var r=0;r<n.length;r++)e?n[r].f(e,t):n[r].t()};d.WxReady(function(e,t){c.wx=null,t?r("微信JsSDK准备失败："+t):(c.wx=e,l(function(){e.startRecord({success:function(){var n=function(e,t){setTimeout(function(){A(function(e){e&&y("停止wx录音"+(t?"[Final]":"")+":"+e,3),!e||t?r():n(1e3,1)})},e)};n(100)},fail:function(e){r("无法微信录音："+e.errMsg)},cancel:function(e){r("用户不允许微信录音："+e.errMsg,!0)}})}))})}else y("检测到连续的RequestPermission调用，wx环境已加入队列，这种操作是不建议的，如果是用户触发，应当开启遮罩层避免重复点击",3);else e()};var u,v,x,_,a=[],f=0,A=function(t,e){e||(u=v=0),c.wx.stopRecord({success:function(){t&&t()},fail:function(e){t&&t("无法结束录音："+e.errMsg)}})},l=function(e,t){!t&&v&&y("录音中，正在kill重试",3),A(function(){setTimeout(e,300)},t)};s.Start=function(e,n,t){var o=c.wx;if(o){if(v)return y("wx正在录音，但又开始新录音，kill掉老的",3),void l(function(){s.Start(e,n,t)});if(u)return y("wx上次Start还未完成，等待重试...",3),void setTimeout(function(){s.Start(e,n,t)},600);var a=++f,r=function(e){u=0,t("开始微信录音失败："+e.errMsg),A()},i=function(t){v=0,u=1,o.startRecord({success:function(){v=1,u=0,c.startTime=Date.now(),c.start=e,y("wx已开始录音"),n()},fail:function(e){t?r(e):l(function(){i(1)})},cancel:r})};i(),c.chunks=[],c.chunkErr="",_=x=null,o.onVoiceRecordEnd({complete:function(e){var t=Date.now();if(x?x(e,"chunk"):e.localId&&c.chunks?c.chunks.push({res:e,duration:t-c.startTime,time:t,from:"chunk"}):y("已忽略wx chunk数据",3,e),_)_();else{y("wx录音超时，正在接续...");var r=function(n){v&&a==f?o.startRecord({success:function(){a==f&&(c.startTime=Date.now(),y("已接续wx录音,中断"+(Date.now()-t)+"ms"))},fail:function(e){if(v&&a==f){var t="无法接续微信录音："+e.errMsg;y(t,1,e),2<n?c.chunkErr=t:(y("尝试重启..."+ ++n),l(function(){r(n)},1))}}}):y("已停止wx录音，拒绝接续",3)};r(0)}}})}else t("请先调用RequestPermission")},s.Stop=function(R,t){var f=c.wx;v=0;var e=!!_;_=null,y("开始停止录音");var m=function(e){t("录音失败[wx]："+(e.errMsg||e))},h=c.start;if(h){var n=Date.now()-c.startTime;if(!e&&59100<n)return y("wx录音即将满1分钟，等待它录满，不然stop不可控...",3),void(_=function(){s.Stop(R,t)});c.start=null;var S={list:[]};h.DownWxMediaData=S;var l=function(){var o=S.list,e=o[0];if(e.duration){for(var t=atob(e.data),n=t.length,r=new Uint8Array(n);n--;)r[n]=t.charCodeAt(n);var a=new Blob([r.buffer],{type:e.mime});return w._SRec=null,y("微信素材服务器端已转码，不支持RecordApp.GetStopUsedRec方法",3),void R(a,e.duration)}var d=[],v=0,i=0,s=0,c=function(){if(s||(s=Date.now()),i>=o.length)return S.decodeTime=Date.now()-s,void function(){v||(v=Date.now());var e=[],t=h.sampleRate/8e3;t<=1?t=1:y("微信arm素材采样率为8000hz（语音音质勉强能听），已自动提升成设置的采样率"+h.sampleRate+"hz，但音质不可能会变好",3);for(var n=0,r=0,o=0;o<d.length;o++)for(var a=d[o],i=0;i<a.length;i++){var s=a[i],c=Math.floor(n);n+=t;for(var u=Math.floor(n),f=(s-r)/(u-c),l=1;c<u;c++,l++)e.push(Math.floor(r+l*f));r=s}var p=Recorder(h).mock(e,h.sampleRate);p.stop(function(e,t){for(var n in S.encodeTime=Date.now()-v,p.set)h[n]=p.set[n];w._SRec=p,R(e,t)},m)}();var e=o[i];e.duration=g[i].duration,e.isAmr=!0;for(var t=atob(e.data),n=t.length,r=new Uint8Array(n);n--;)r[n]=t.charCodeAt(n);Recorder.AMR.decode(r,function(e){d.push(e),i++,c()},function(e){m("AMR音频"+(i+1)+"无法解码:"+e)})};Recorder.AMR?c():m("未加载AMR转换引擎")},p=[],r=function(){if(R){for(var n=[],e=0;e<g.length;e++)n.push(g[e].res.localId);if(y("结束录音共"+n.length+"段，开始上传下载",n,g),n.length){var r=0,o=0,a=function(){S.downTime=Date.now()-o,y("开始转码..."),l()},i=function(t){if(o||(o=Date.now()),r>=p.length)a();else{var e=p[r];d.DownWxMedia({mediaId:e,transform_mediaIds:p.join(","),transform_type:h.type,transform_bitRate:h.bitRate,transform_sampleRate:h.sampleRate},function(e){S.list.push(e),e.duration?a():/amr/i.test(e.mime)?(r++,i()):m("微信服务器返回了未知音频类型："+e.mime)},function(e){2<(t=t||0)?m("下载微信音频失败："+e):(y("DownWxMedia失败，重试..."+ ++t,1,e),i(t))})}},s=0,c=function(t){if(s>=n.length)return S.uploadTime=Date.now()-u,y("开始下载微信素材..."),void i();var e=n[s];y("wx上传本地录音["+s+"] wx.playVoice({localId:'"+e+"'})"),f.uploadVoice({localId:e,isShowProgressTips:0,fail:function(e){2<(t=t||0)?m("微信uploadVoice失败["+s+"]："+e.errMsg):(y("uploadVoice失败，重试..."+ ++t,1,e),c(t))},success:function(e){var t=e.serverId;y("上传OK serverId:"+t),p.push(t),s++,c()}})},u=Date.now();c()}else m("未获得任何录音")}else m("仅清理资源")},g=c.chunks;if(c.chunkErr)return y(c.chunkErr,1,g),void m("录制失败，已录制"+g.length+"分钟，但后面出错："+c.chunkErr);if(e)r();else{if(g.length&&Date.now()-g[g.length-1].time<900)return y("丢弃结尾未停止太短录音",3),A(),void r();x=function(e,t){x=null;var n=Date.now();e.localId?g.push({res:e,duration:n-c.startTime,time:n,from:t}):y("已忽略"+t+"数据",3,e),c.chunks=null,r()},f.stopRecord({fail:function(e){x=null,g.length&&n<3e3?(y("停止录音出错，但后续录音太短，已忽略此错误："+e.errMsg,3),r()):m(e)},success:function(e){x&&x(e,"stop")}})}}else m("未开始录音")}}(),function(){"use strict";var i=RecordApp,f=i.CLog,l=i.Platforms.Native,s=l.Config;l.IsInit=!0;var p=window.NativeRecordReceivePCM=function(e,t){var n=p.rec;if(n){n._appStart||n.envStart({envName:l.Key,canProcess:l.CanProcess()},t),n._appStart=1;for(var r,o=atob(e),a=o.length,i=new Int16Array(a/2),s=0,c=0,u=0;u+2<=a;c++,u+=2)r=(o.charCodeAt(u)|o.charCodeAt(u+1)<<8)<<16>>16,i[c]=r,s+=Math.abs(r);n.envIn(i,s)}else f("未开始录音，但收到Native PCM数据",3)};try{window.top.NativeRecordReceivePCM=p}catch(e){var t=function(){f("检测到跨域iframe，NativeRecordReceivePCM无法注入到顶层，已监听postMessage转发兼容传输数据，请自行实现将top层接收到数据转发到本iframe（不限层），不然无法接收到录音数据",3)};setTimeout(t,8e3),t(),addEventListener("message",function(e){var t=e.data;t&&"NativeRecordReceivePCM"==t.type&&(t=t.data,p(t.pcmDataBase64,t.sampleRate))})}l.RequestPermission=function(e,t){s.JsBridgeRequestPermission(e,t)},l.Start=function(e,t,n){p.param=e;var r=Recorder(e);r.set.disableEnvInFix=!0,p.rec=r,i.__Rec=r,s.JsBridgeStart(e,t,n)},l.Stop=function(o,t){var a=function(e){t("录音失败[Native]："+e),p.rec=null,i.__Rec=null};s.JsBridgeStop(function(){var n=p.rec;if(p.rec=null,n){f("rec encode start: pcm:"+n.recSize+" src:"+n.srcSampleRate+" set:"+JSON.stringify(p.param));var r=function(){for(var e in n.set)p.param[e]=n.set[e]};if(!o)return r(),void a("仅清理资源");n.stop(function(e,t){f("rec encode end"),r(),i._SRec=n,o(e,t),i.__Rec=null},function(e){r(),a(e)})}else a("未开始录音")},a)}}();