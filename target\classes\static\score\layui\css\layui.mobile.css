/**
 
 @Name: layui mobile
 @Author: 贤心
 @Site: http://www.layui.com/mobile/
 
 */
 
/* reset */
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,input,button,textarea,p,blockquote,th,td,form,legend{margin:0; padding:0; -webkit-tap-highlight-color:rgba(0,0,0,0)}
html{font:12px 'Helvetica Neue','PingFang SC',STHeitiSC-Light,Helvetica,Arial,sans-serif; -ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;}
a,button,input{-webkit-tap-highlight-color:rgba(255,0,0,0);}
a{text-decoration: none; background:transparent}
a:active,a:hover{outline:0}
table{border-collapse:collapse;border-spacing:0}
li{list-style:none;}
b,strong{font-weight:700;}
h1, h2, h3, h4, h5, h6{font-weight:500;}
address,cite,dfn,em,var{font-style:normal;}
dfn{font-style:italic}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
img{border:0; vertical-align: bottom}
button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0; outline: 0;}
button,select{text-transform:none}
select{-webkit-appearance: none; border:none;}
input{line-height:normal; }
input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}
input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}
input[type=search]{-webkit-appearance:textfield;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box}
input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}
label,input{vertical-align: middle;}


/** 图标字体 **/
@font-face {font-family: 'layui-icon';
  src: url('../font/iconfont.eot?v=1.0.7');
  src: url('../font/iconfont.eot?v=1.0.7#iefix') format('embedded-opentype'),
  url('../font/iconfont.woff?v=1.0.7') format('woff'),
  url('../font/iconfont.ttf?v=1.0.7') format('truetype'),
  url('../font/iconfont.svg?v=1.0.7#iconfont') format('svg');
}
                    
.layui-icon{
  font-family:"layui-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


/** 基础通用 **/
/* 消除第三方ui可能造成的冲突 */.layui-box, .layui-box *{-webkit-box-sizing: content-box !important; -moz-box-sizing: content-box !important; box-sizing: content-box !important;}
.layui-border-box, .layui-border-box *{-webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
.layui-inline{position: relative; display: inline-block; *display:inline; *zoom:1; vertical-align: middle;}
/* 三角形 */.layui-edge{position: absolute; width: 0; height: 0; border-style: dashed; border-color: transparent; overflow: hidden;}
/* 单行溢出省略 */.layui-elip{text-overflow: ellipsis; overflow: hidden; white-space: nowrap;}
/* 屏蔽选中 */.layui-unselect{-moz-user-select: none; -webkit-user-select: none; -ms-user-select: none;}
.layui-disabled,.layui-disabled:active{background-color: #d2d2d2 !important; color: #fff !important; cursor: not-allowed !important;}
/* 纯圆角 */.layui-circle{border-radius: 100%;}
.layui-show{display: block !important;}
.layui-hide{display: none !important;}


.layui-upload-iframe{position: absolute; width: 0px; height: 0px; border: 0px; visibility: hidden;}
.layui-upload-enter{border: 1px solid #009E94; background-color: #009E94; color: #fff; -webkit-transform: scale(1.1); transform: scale(1.1);}


/* 弹出动画 */
@-webkit-keyframes layui-m-anim-scale { /* 默认 */
	0% {opacity: 0; -webkit-transform: scale(.5); transform: scale(.5)}
	100% {opacity: 1; -webkit-transform: scale(1); transform: scale(1)}
}
@keyframes layui-m-anim-scale { /* 由小到大 */
	0% {opacity: 0; -webkit-transform: scale(.5); transform: scale(.5)}
	100% {opacity: 1; -webkit-transform: scale(1); transform: scale(1)}
}
.layui-m-anim-scale{animation-name:  layui-m-anim-scale; -webkit-animation-name:  layui-m-anim-scale;}

@-webkit-keyframes layui-m-anim-up{ /* 从下往上 */
  0%{opacity: 0; -webkit-transform: translateY(800px); transform: translateY(800px)}
  100%{opacity: 1; -webkit-transform: translateY(0); transform: translateY(0)}
}
@keyframes layui-m-anim-up{
  0%{opacity: 0; -webkit-transform: translateY(800px); transform: translateY(800px)}
  100%{opacity: 1; -webkit-transform: translateY(0); transform: translateY(0)}
}
.layui-m-anim-up{-webkit-animation-name: layui-m-anim-up; animation-name: layui-m-anim-up}

@-webkit-keyframes layui-m-anim-left{ /* 从右往左 */
  0%{-webkit-transform: translateX(100%); transform: translateX(100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
@keyframes layui-m-anim-left{
  0%{-webkit-transform: translateX(100%); transform: translateX(100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
.layui-m-anim-left{-webkit-animation-name: layui-m-anim-left; animation-name: layui-m-anim-left}

@-webkit-keyframes layui-m-anim-right{ /* 从左往右 */
  0%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
@keyframes layui-m-anim-right{
  0%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
  100%{-webkit-transform: translateX(0); transform: translateX(0)}
}
.layui-m-anim-right{-webkit-animation-name: layui-m-anim-right; animation-name: layui-m-anim-right}

@-webkit-keyframes layui-m-anim-lout{ /* 往左收缩 */
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
}
@keyframes layui-m-anim-lout{
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(-100%); transform: translateX(-100%)}
}
.layui-m-anim-lout{-webkit-animation-name: layui-m-anim-lout; animation-name: layui-m-anim-lout}

@-webkit-keyframes layui-m-anim-rout{ /* 往右收缩 */
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(100%); transform: translateX(100%)}
}
@keyframes layui-m-anim-rout{
  0%{-webkit-transform: translateX(0); transform: translateX(0)}
  100%{-webkit-transform: translateX(100%); transform: translateX(100%)}
}
.layui-m-anim-rout{-webkit-animation-name: layui-m-anim-rout; animation-name: layui-m-anim-rout}


/** layer mobile */
.layui-m-layer{position:relative; z-index: 19891014;}
.layui-m-layer *{-webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box;}
.layui-m-layershade,
.layui-m-layermain{position:fixed; left:0; top:0; width:100%; height:100%;}
.layui-m-layershade{background-color:rgba(0,0,0, .7); pointer-events:auto;}
.layui-m-layermain{display:table; font-family: Helvetica, arial, sans-serif; pointer-events: none;}
.layui-m-layermain .layui-m-layersection{display:table-cell; vertical-align:middle; text-align:center;}
.layui-m-layerchild{position:relative; display:inline-block; text-align:left; background-color:#fff; font-size:14px; border-radius: 5px; box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);  pointer-events:auto;  -webkit-overflow-scrolling: touch;}
.layui-m-layerchild{-webkit-animation-fill-mode: both; animation-fill-mode: both; -webkit-animation-duration: .2s; animation-duration: .2s;}

.layui-m-layer0 .layui-m-layerchild{width: 90%; max-width: 640px;}
.layui-m-layer1 .layui-m-layerchild{border:none; border-radius:0;}
.layui-m-layer2 .layui-m-layerchild{width:auto; max-width:260px; min-width:40px; border:none; background: none; box-shadow: none; color:#fff;}
.layui-m-layerchild h3{padding: 0 10px; height: 60px; line-height: 60px; font-size:16px; font-weight: 400;  border-radius: 5px 5px 0 0; text-align: center;}
.layui-m-layerchild h3,
.layui-m-layerbtn span{ text-overflow:ellipsis; overflow:hidden; white-space:nowrap;}
.layui-m-layercont{padding: 50px 30px; line-height: 22px; text-align:center;}
.layui-m-layer1 .layui-m-layercont{padding:0; text-align:left;}
.layui-m-layer2 .layui-m-layercont{text-align:center; padding: 0; line-height: 0;}
.layui-m-layer2 .layui-m-layercont i{width:25px; height:25px; margin-left:8px; display:inline-block; background-color:#fff; border-radius:100%;}
.layui-m-layer2 .layui-m-layercont p{margin-top: 20px;}

/* loading */
@-webkit-keyframes layui-m-anim-loading{
    0%,80%,100%{transform:scale(0); -webkit-transform:scale(0)}
    40%{transform:scale(1); -webkit-transform:scale(1)}
}
@keyframes layui-m-anim-loading{
    0%,80%,100%{transform:scale(0); -webkit-transform:scale(0)}
    40%{transform:scale(1); -webkit-transform:scale(1)}
}
.layui-m-layer2 .layui-m-layercont i{-webkit-animation: layui-m-anim-loading 1.4s infinite ease-in-out; animation: layui-m-anim-loading 1.4s infinite ease-in-out; -webkit-animation-fill-mode: both; animation-fill-mode: both;}

.layui-m-layer2 .layui-m-layercont i:first-child{margin-left:0; -webkit-animation-delay: -.32s; animation-delay: -.32s;}
.layui-m-layer2 .layui-m-layercont i.layui-m-layerload{-webkit-animation-delay: -.16s; animation-delay: -.16s;}
.layui-m-layer2 .layui-m-layercont>div{line-height:22px; padding-top:7px; margin-bottom:20px; font-size: 14px;}
.layui-m-layerbtn{display: box; display: -moz-box; display: -webkit-box; width: 100%; position:relative; height: 50px; line-height: 50px; font-size: 0; text-align:center;  border-top:1px solid #D0D0D0; background-color: #F2F2F2; border-radius: 0 0 5px 5px;}
.layui-m-layerbtn span{position:relative; display: block; -moz-box-flex: 1; box-flex: 1; -webkit-box-flex: 1;  text-align:center; font-size:14px; border-radius: 0 0 5px 5px; cursor:pointer;}
.layui-m-layerbtn span[yes]{color: #40AFFE;}
.layui-m-layerbtn span[no]{border-right: 1px solid #D0D0D0; border-radius: 0 0 0 5px;}
.layui-m-layerbtn span:active{background-color: #F6F6F6;}
.layui-m-layerend{position:absolute; right:7px; top:10px; width:30px; height:30px; border: 0; font-weight:400; background: transparent; cursor: pointer; -webkit-appearance: none; font-size:30px;}
.layui-m-layerend::before, .layui-m-layerend::after{position:absolute; left:5px; top:15px; content:''; width:18px; height:1px; background-color:#999; transform:rotate(45deg); -webkit-transform:rotate(45deg); border-radius: 3px;}
.layui-m-layerend::after{transform:rotate(-45deg);  -webkit-transform:rotate(-45deg);}

/* 底部对话框风格 */
body .layui-m-layer .layui-m-layer-footer{position: fixed; width: 95%; max-width: 100%; margin: 0 auto; left:0; right: 0; bottom: 10px; background: none;}
.layui-m-layer-footer .layui-m-layercont{padding: 20px; border-radius: 5px 5px 0 0; background-color: rgba(255,255,255,.8);}
.layui-m-layer-footer .layui-m-layerbtn{display: block; height: auto; background: none; border-top: none;}
.layui-m-layer-footer .layui-m-layerbtn span{background-color: rgba(255,255,255,.8);}
.layui-m-layer-footer .layui-m-layerbtn span[no]{color: #FD482C; border-top: 1px solid #c2c2c2; border-radius: 0 0 5px 5px;}
.layui-m-layer-footer .layui-m-layerbtn span[yes]{margin-top: 10px; border-radius: 5px;}

/* 通用提示 */
body .layui-m-layer .layui-m-layer-msg{width: auto; max-width: 90%; margin: 0 auto; bottom: -150px; background-color: rgba(0,0,0,.7); color: #fff;}
.layui-m-layer-msg .layui-m-layercont{padding: 10px 20px;}




