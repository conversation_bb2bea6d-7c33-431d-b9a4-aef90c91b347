package com.chaoxing.academic.service.cultivation.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import cn.hutool.poi.excel.style.StyleUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaoxing.academic.api.course.CourseApi;
import com.chaoxing.academic.api.passport.UserApi;
import com.chaoxing.academic.api.print.PrintApi;
import com.chaoxing.academic.design.template.bo.FormTopBtnBO;
import com.chaoxing.academic.entity.approve.cultivation.CourseManageApprove;
import com.chaoxing.academic.entity.approve.cultivation.TeachPlanChangeApprove;
import com.chaoxing.academic.entity.approve.cultivation.subform.Kkszsp_kksz;
import com.chaoxing.academic.entity.dto.cultivation.CourseInformationDto;
import com.chaoxing.academic.entity.form.basic.*;
import com.chaoxing.academic.entity.form.basic.subform.Xnxqlczbd;
import com.chaoxing.academic.entity.form.cultivation.*;
import com.chaoxing.academic.entity.form.cultivation.subform.Jxjhkc_jc;
import com.chaoxing.academic.entity.form.cultivation.subform.Kksz;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsInfoDetail;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsTeacher;
import com.chaoxing.academic.entity.po.cultivation.CultivationMaintainTeachPlan;
import com.chaoxing.academic.entity.po.timetable.Timetable;
import com.chaoxing.academic.entity.po.timetable.TimetableLesson;
import com.chaoxing.academic.entity.po.user.User;
import com.chaoxing.academic.entity.vo.R;
import com.chaoxing.academic.entity.vo.form.DataToFromJsonVo;
import com.chaoxing.academic.entity.vo.form.FormRightBtnVO;
import com.chaoxing.academic.enums.SearchStrBodyType;
import com.chaoxing.academic.service.cultivation.ClassStartsInfoDetailService;
import com.chaoxing.academic.service.cultivation.ClassStartsTeacherService;
import com.chaoxing.academic.service.cultivation.CultivationMaintainTeachPlanService;
import com.chaoxing.academic.service.cultivation.CultivationPopService;
import com.chaoxing.academic.service.timetable.TimetableLessonService;
import com.chaoxing.academic.service.timetable.TimetableService;
import com.chaoxing.academic.utils.ConversionNumberUtil;
import com.chaoxing.academic.utils.MyUtils;
import com.chaoxing.academic.utils.NumberUtils;
import com.chaoxing.academic.utils.StringUtils;
import com.chaoxing.academic.utils.UserUtils;
import com.chaoxing.academic.utils.form.FormUtils;
import com.chaoxing.academic.utils.form.basic.SemesterUtils;
import com.chaoxing.academic.utils.task.thread.ThreadTaskUtils;
import com.chaoxing.form.ApproveTemplate;
import com.chaoxing.form.FormTemplate;
import com.chaoxing.form.annotation.Form;
import com.chaoxing.form.constant.FormComponentConstants;
import com.chaoxing.form.param.IdSearchParam;
import com.chaoxing.form.param.SearchParam;
import com.chaoxing.form.param.UpdateParam;
import com.chaoxing.form.param.approve.AprvAppListParam;
import com.chaoxing.form.param.approve.AprvIdSearchParam;
import com.chaoxing.form.param.approve.AprvSearchParam;
import com.chaoxing.form.pojo.ComptIdValue;
import com.chaoxing.form.pojo.IdName;
import com.chaoxing.form.pojo.SearchStrBody;
import com.chaoxing.form.util.FormUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFShape;
import org.apache.poi.hssf.usermodel.HSSFSimpleShape;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CultivationPopServiceImpl implements CultivationPopService {

    @Autowired
    TimetableService timetableService;

    @Autowired
    TimetableLessonService timetableLessonService;

    @Autowired
    ClassStartsInfoDetailService classStartsInfoDetailService;

    @Autowired
    ClassStartsTeacherService classStartsTeacherService;

    @Resource
    CultivationMaintainTeachPlanService maintainTeachPlanService;

    @Override
    public List<OpenCourseRecordForm> getFormData(long fid, int week, String term) {
        String appName = OpenCourseRecordForm.class.getAnnotation(Form.class).formAlias();
        SearchParam searchParam = SearchParam.builder().deptId((int) fid).appName(appName).build();
        SearchStrBody searchStrBody = SearchStrBody.and();
        searchStrBody.createAndAdd(OpenCourseRecordForm::getGkkjl_zc).eq(week);
        searchStrBody.createAndAdd(OpenCourseRecordForm::getGkkjl_xnxq).eq(term);
        int count = FormTemplate.count(searchParam, searchStrBody);
        int totalPage = (count + 100 - 1) / 100;
        List<OpenCourseRecordForm> list = new ArrayList<>();
        for (int i = 0; i < totalPage; i++) {
            searchParam.setCpage(i + 1);
            searchParam.setOnlyReturnCount(0);
            searchParam.setPageSize(100);
            List<OpenCourseRecordForm> formList = FormTemplate.search(searchParam, searchStrBody, OpenCourseRecordForm.class);
            list.addAll(formList);
        }
        return list;
    }

    @Override
    public R<JSONObject> exportDataToExcel(Integer weekly, Integer type, String term) {
        User user = UserUtils.user();
        if (user == null) {
            return R.fail("获取用户信息异常");
        }
        JSONObject schoolInfo = UserApi.getSchoolInfo(Math.toIntExact(user.getFid()));
        String schoolName = MyUtils.isNotEmpty(schoolInfo) && schoolInfo.containsKey("name") ? schoolInfo.getString("name") : "";
        Timetable timetable = timetableService.inuseTimetable(user.getFid());
        if (timetable == null) {
            return R.fail("获取课表信息异常");
        }
        JSONObject json = new JSONObject();
        String id = IdUtil.fastSimpleUUID();
        String filePath = "/mnt/mfs/academic/" + id + ".xls";
        json.put("file", id);
        try (ExcelWriter writer = ExcelUtil.getWriter(filePath)) {
            //设置表格样式
            CellStyle bodyCellStyle = writer.getStyleSet().getCellStyle();
            bodyCellStyle.setWrapText(true);
            //移除默认sheet
            writer.getSheet().getWorkbook().removeSheetAt(0);
            List<List<TimetableLesson>> lessons = timetableLessonService.oneWholeWeekLessons(timetable);
            int start = type == 2 ? weekly - 1 : 0;
            for (int i = start; i < weekly; i++) {
                String sheetName = "第" + (i + 1) + "周";
                writer.setSheet(sheetName);
                String title = schoolName + "公开课安排\n" + term + "学期" + "（" + sheetName + "）";
                writer.writeCellValue(0, 0, title).merge(timetable.getWeekDays());
                CellStyle titleCellStyle = writer.createCellStyle(0, 0);
                Font font = writer.createFont();
                font.setBold(true);
                titleCellStyle.setFont(font);
                titleCellStyle.setWrapText(true);
                titleCellStyle.setAlignment(HorizontalAlignment.CENTER);
                titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                setCellStyle(writer, 0, 0, null, null, 50);
                List<OpenCourseRecordForm> list = getFormData(user.getFid(), i + 1, term);
                int y = 0;
                for (int j = 0; j < lessons.size(); j++) {
                    if (lessons.get(j).get(0).getLessonNum() == 0) {
                        continue;
                    }
                    y++;
                    String jc = String.valueOf(y);
                    setCellStyle(writer, 0, y + 1, null, lessons.get(j).get(0).getLessonNumName(), 50);
                    for (int k = 0; k < timetable.getWeekDays(); k++) {
                        String xq = String.valueOf(k + 1);
                        String week = "星期" + NumberChineseFormatter.format(k + 1, false);
                        writeSecHeadRow(writer);
                        writer.writeCellValue(0, 1, "                          星期\n\n   节次");
                        setCellStyle(writer, k + 1, 1, null, week, 50);
                        CellStyle cellStyle = writer.createCellStyle(k + 1, y + 1);
                        cellStyle.setAlignment(HorizontalAlignment.LEFT);
                        cellStyle.setWrapText(true);
                        StyleUtil.setBorder(cellStyle, BorderStyle.THIN, IndexedColors.BLACK);
                        StringBuilder content = new StringBuilder();
                        List<OpenCourseRecordForm> filterList = list.stream().filter(t -> t.getGkkjl_xq().equals(xq) && t.getGkkjl_jc().equals(jc)).collect(Collectors.toList());
                        for (OpenCourseRecordForm openCourseRecordForm : filterList) {
                            String subject = StringUtils.isNotBlank(openCourseRecordForm.getGkkjl_kkxkz()) ? openCourseRecordForm.getGkkjl_kkxkz() : "";
                            String teacher = openCourseRecordForm.getGkkjl_skjs() != null ? openCourseRecordForm.getGkkjl_skjs().getUname() : "";
                            String topic = StringUtils.isNotBlank(openCourseRecordForm.getGkkjl_kt()) ? openCourseRecordForm.getGkkjl_kt() : "";
                            String clazz = StringUtils.isNotBlank(openCourseRecordForm.getGkkjl_bj()) ? openCourseRecordForm.getGkkjl_bj() : "";
                            String address = StringUtils.isNotBlank(openCourseRecordForm.getGkkjl_js()) ? openCourseRecordForm.getGkkjl_js() : "";
                            String link = StringUtils.isNotBlank(openCourseRecordForm.getGkkjl_xszblj()) ? openCourseRecordForm.getGkkjl_xszblj() : "";
                            String course = StringUtils.isNotBlank(openCourseRecordForm.getGkkjl_kcmc()) ? openCourseRecordForm.getGkkjl_kcmc() : "";
                            content.append("学科：").append(StrUtil.join("/", subject)).append("\n");
                            content.append("教师：").append(StrUtil.join("/", teacher)).append("\n");
                            content.append("课程：").append(StrUtil.join("/", course)).append("\n");
                            content.append("课题：").append(StrUtil.join("/", topic)).append("\n");
                            content.append("班级：").append(StrUtil.join("/", clazz)).append("\n");
                            content.append("地点：").append(StrUtil.join("/", address)).append("\n");
                            content.append("链接：").append(StrUtil.join("/", link)).append("\n\n");
                            setCellStyle(writer, k + 1, y + 1, cellStyle, content.toString(), 100);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("导出课表异常->", e);
        }
        return R.success(json);
    }

    @Override
    public R<JSONObject> exportCourseDataToExcel(Integer fid, String semester, Integer dataType) {
        JSONObject json = new JSONObject();
        JSONObject formJson = FormUtils.getFormFiled(fid, CourseInformationForm.ALIAS);
        if (MyUtils.isEmpty(formJson)) {
            return R.fail("获取表单信息异常");
        }
        String id = IdUtil.fastSimpleUUID();
        String filePath = "/mnt/mfs/academic/" + id + ".xls";
        json.put("file", id);
        List<CourseInformationDto> allList = new ArrayList<>();
        if (dataType == 1 || dataType == 2) {
            exportStatisticsExcel(fid, dataType, semester, allList, formJson, filePath);
        } else if (dataType == 3) {
            exportEquipmentExcel(fid, semester, filePath);
        } else if (dataType == 4) {
            exportCourseDivide(fid, semester, filePath);
        }
        return R.success(json);
    }

    @Override
    public void appointTeacher(FormTopBtnBO formTopBtnBO, String formUserId, String formField, String formFieldVal, Integer syncScoreTeacher) {
        List<String> teachSectionArray;
        List<IdName> teacherContacts = new ArrayList<>();
        List<String> teacherNoArray;
        List<String> teacherNameArray;
        String teacherNo, teachSection, teacherName;
        if (StringUtils.isNotBlank(formUserId)) {
            IdSearchParam idSearchParam = IdSearchParam.builder().deptId(formTopBtnBO.getFid()).appName(TeacherInfoForm.ALIAS).formUserIds(formUserId).build();
            List<TeacherInfoForm> teacherInfoFormList = FormTemplate.searchById(idSearchParam, TeacherInfoForm.class);
            teachSectionArray = teacherInfoFormList.stream().map(TeacherInfoForm::getJsjbxx_jysks).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            teacherContacts = teacherInfoFormList.stream().map(TeacherInfoForm::getJsjbxx_xmlxr).filter(Objects::nonNull).collect(Collectors.toList());
            teacherNoArray = teacherInfoFormList.stream().map(TeacherInfoForm::getJsjbxx_jsgh).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            teacherNameArray = teacherInfoFormList.stream().map(TeacherInfoForm::getJsjbxx_xm).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            teacherNo = CollUtil.join(teacherNoArray, ",");
            teachSection = CollUtil.join(teachSectionArray, ",");
            teacherName = CollUtil.join(teacherNameArray, ",");
        } else {
            teachSection = "";
            teacherName = "";
            teacherNo = "";
        }
        List<IdName> finalTeacherContacts = teacherContacts;
        if (StringUtils.isNotBlank(formField)) {
            FormUtils.queryIdPagesIterator(formTopBtnBO.getFid(), formTopBtnBO.getUid(), formTopBtnBO.getQueryId(), list -> {
                for (CourseManageForm courseManageForm : list) {
                    JSONArray formArray = new JSONArray();
                    JSONArray childArray = new JSONArray();
                    String cFormUserId = String.valueOf(courseManageForm.getRowInfo().getFormUserId());
                    List<Kksz> kkszList = MyUtils.isNotEmpty(courseManageForm.getKksz()) ? courseManageForm.getKksz() : Collections.singletonList(new Kksz());
                    String credit = "";
                    if ("kkgl_xf".equals(formField)) {
                        credit = StringUtils.isNotBlank(formFieldVal) ? formFieldVal : credit;
                        JSONArray creditValArray = new JSONArray().fluentAdd(new JSONObject().fluentPut("val", credit));
                        JSONObject creditJson = new JSONObject().fluentPut("alias", "kkgl_xf").fluentPut("compt", "numberinput").fluentPut("values", creditValArray);
                        formArray.fluentAdd(creditJson);
                    } else {
                        childArray = packageFormJson(formField, formFieldVal, kkszList, finalTeacherContacts, cFormUserId, syncScoreTeacher);
                    }
                    if (MyUtils.isNotEmpty(childArray)) {
                        JSONObject json = new JSONObject().fluentPut("alias", "kksz").fluentPut("compt", "detailcombox").fluentPut("formDataList", childArray);
                        formArray.fluentAdd(json);
                    }
                    UpdateParam updateParam = UpdateParam.builder().fid(formTopBtnBO.getFid()).formAlias(CourseManageForm.ALIAS).formUserId(cFormUserId).autoFilterErrorFields(true).build();
                    updateParam.setFormData(JSON.toJSONString(formArray));
                    FormUtil.execute(updateParam);
                }
            }, CourseManageForm.class);
            return;
        }
        JSONArray formData = new JSONArray();
        if (StringUtils.isBlank(formUserId)) {
            formData.fluentAdd(new ComptIdValue("kkxxb_skjsxm", FormComponentConstants.lian_xi_ren))
                    .fluentAdd(new ComptIdValue("kkxxb_skjsgh", FormComponentConstants.dan_hang_shu_ru))
                    .fluentAdd(new ComptIdValue("kkxxb_skjs", FormComponentConstants.xia_la_kuang));
        } else {
            CourseInformationForm cif = new CourseInformationForm().setKkxxb_kkjys(teachSection).setKkxxb_skjsxm(finalTeacherContacts).setKkxxb_skjsgh(teacherNo).setKkxxb_skjs(teacherName);
            if (syncScoreTeacher == 1) {
                cif.setKkxxb_cjlrjs(finalTeacherContacts).setKkxxb_cjlrjsxm(teacherName).setKkxxb_cjlrjsgh(teacherNo);
            }
            String formDataStr = FormUtil.reflectFormData(cif);
            formData = JSON.parseArray(formDataStr);
        }
        JSONArray finalFormData = formData;
        FormUtils.queryIdPagesIterator(formTopBtnBO.getFid(), formTopBtnBO.getUid(), formTopBtnBO.getQueryId(), list -> {
            for (CourseInformationForm courseInformationForm : list) {
                String cFormUserId = String.valueOf(courseInformationForm.getRowInfo().getFormUserId());
                UpdateParam updateParam = UpdateParam.builder().fid(formTopBtnBO.getFid()).formAlias(CourseInformationForm.ALIAS).autoFilterErrorFields(true).formUserId(cFormUserId).build();
                updateParam.setFormData(JSON.toJSONString(finalFormData, SerializerFeature.DisableCircularReferenceDetect));
                FormUtil.execute(updateParam);
            }
        }, CourseInformationForm.class);
    }

    @Override
    public R termCreditSummary(FormRightBtnVO formRightBtnVO) {
        if (ObjectUtil.hasEmpty(formRightBtnVO.getFid(), formRightBtnVO.getFormUserId(), formRightBtnVO.getUid())) {
            return R.fail("参数异常");
        }
        TrainingProgramForm trainingProgramForm = FormUtils.getById(formRightBtnVO.getFid(), "263569", String.valueOf(formRightBtnVO.getFormUserId()), TrainingProgramForm.class);
        if (trainingProgramForm == null) {
            return R.fail("数据异常");
        }
        List<MajorCourseSetForm> list = FormUtils.listAll(SearchStrBodyType.AND, formRightBtnVO.getFid(), MajorCourseSetForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(MajorCourseSetForm::getNj).eq(trainingProgramForm.getPyfagl_nj());
            searchStrBody.createAndAdd(MajorCourseSetForm::getZyksz_zybh).eq(trainingProgramForm.getPyfagl_zybh());
        }, MajorCourseSetForm.class);
        return R.success(list);
    }

    @Override
    public R getSubjectGroupData(Integer fid) {
        if (ObjectUtil.hasEmpty(fid)) {
            return R.fail("参数异常");
        }
        List<SubjectGroupForm> list = FormUtils.listAll(SearchStrBodyType.AND, fid, "jyssj", null, SubjectGroupForm.class);
        return R.success(list);
    }

    /**
     * @param semester  学年学期
     * @param teacherNo 教师工号
     * @param fid       单位id
     * @description 获取教师周课时
     * <AUTHOR>
     * @date 2024/6/4 15:54
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @Override
    public R getTeacherWeekHour(String semester, String teacherNo, Integer fid) {
        if (ObjectUtil.hasEmpty(semester, teacherNo, fid)) {
            return R.fail("参数异常");
        }
        JSONObject json = new JSONObject();
        TeacherInfoForm teacherInfoForm = FormUtils.getOne(SearchStrBodyType.AND, fid, TeacherInfoForm.ALIAS, searchStrBody ->
                searchStrBody.createAndAdd(TeacherInfoForm::getJsjbxx_jsgh).eq(teacherNo), TeacherInfoForm.class);
        TeacherCourseSummaryForm info = FormUtils.getOne(SearchStrBodyType.AND, fid, TeacherCourseSummaryForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(TeacherCourseSummaryForm::getSkjskkhz_kkxq).eq(semester);
            searchStrBody.createAndAdd(TeacherCourseSummaryForm::getSkjskkhz_skjsgh).eq(teacherNo);
        }, TeacherCourseSummaryForm.class);
        json.put("weekHour", info != null ? info.getSkjskkhz_zxs() : "");
        if (teacherInfoForm != null && teacherInfoForm.getJsjbxx_xmlxr() != null) {
            double total = 0;
            List<CourseManageForm> courseManageForms = FormUtils.listAll(SearchStrBodyType.AND, fid, CourseManageForm.ALIAS, searchStrBody -> {
                searchStrBody.createAndAdd(CourseManageForm::getKkgl_shzt).neq("未通过");
                searchStrBody.createAndAdd(CourseManageForm::getKkgl_kkxq).eq(semester);
                searchStrBody.createAndAdd(Kksz::getKkgl_skjs).match(teacherInfoForm.getJsjbxx_xmlxr().getPuid());
            }, CourseManageForm.class);
            if (courseManageForms.isEmpty()) {
                json.put("preWeekHour", 0.0);
                return R.success(json);
            }
            // 计算总课时
            for (CourseManageForm form : courseManageForms) {
                List<Kksz> kkszList = form.getKksz();
                if (kkszList == null || kkszList.isEmpty()) {
                    continue;
                }
                Integer teacherUid = teacherInfoForm.getJsjbxx_xmlxr().getPuid();
                total += kkszList.stream()
                        .filter(kksz -> kksz != null && kksz.getKkgl_skjs() != null &&
                                kksz.getKkgl_skjs().stream().anyMatch(idName ->
                                        Objects.equals(teacherUid, idName.getPuid())))
                        .filter(kksz -> StringUtils.isNotBlank(kksz.getKkgl_zks()))
                        .mapToDouble(kksz -> {
                            try {
                                return Double.parseDouble(kksz.getKkgl_zks());
                            } catch (NumberFormatException e) {
                                log.warn("教师[{}]课时数据格式错误: {}",
                                        teacherInfoForm.getJsjbxx_xm(), kksz.getKkgl_zks());
                                return 0.0;
                            }
                        }).sum();
            }
            json.put("preWeekHour", total);
        }
        return R.success(json);
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @description 获取开课总学时统计数据
     * <AUTHOR>
     * @date 2024/7/25 9:40
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @Override
    public R totalHoursStatisticsData(FormRightBtnVO formRightBtnVO) {
        JSONObject json;
        if (Objects.equals(CourseManageApprove.ALIAS, formRightBtnVO.getAppName())) {
            json = getCourseManageApproveData(formRightBtnVO);
        } else {
            json = getCourseManageData(formRightBtnVO);
        }
        return R.success(json);
    }

    @SneakyThrows
    @Override
    public R downImportTpl(Integer fid, String fileName) {
        JSONObject json = new JSONObject();
        String resourceLocation = ResourceUtils.CLASSPATH_URL_PREFIX + "static/templateFile/" + fileName;
        String id = IdUtil.fastSimpleUUID();
        String filePath = "/mnt/mfs/academic/" + id + ".xlsx";
        File srcFile = ResourceUtils.getFile(resourceLocation);
        FileUtil.copyFile(srcFile, new File(filePath), StandardCopyOption.REPLACE_EXISTING);
        json.put("file", id);
        return R.success(json);
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @param operateType    1 修改教学计划 2 停用教学计划
     * @description 教学计划变更操作校验
     * <AUTHOR>
     * @date 2024/10/23 15:24
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @Override
    public R teachPlanChangeOperateVerify(FormRightBtnVO formRightBtnVO, Integer operateType) {
        CourseInformationForm info = FormUtils.getOne(SearchStrBodyType.AND, formRightBtnVO.getFid(), CourseInformationForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(CourseInformationForm::getJw_jxjhformUserId).eq(formRightBtnVO.getFormUserId());
        }, CourseInformationForm.class);
        TeachPlanForm tpf = FormUtils.getById(formRightBtnVO.getFid(), TeachPlanForm.ALIAS, String.valueOf(formRightBtnVO.getFormUserId()), TeachPlanForm.class);
        JSONObject precast = getTeachPlanChangePrecast(tpf, operateType);
        AprvAppListParam appListParam = AprvAppListParam.builder().deptId(formRightBtnVO.getFid()).formAlias(TeachPlanChangeApprove.ALIAS).build();
        JSONObject json = ApproveTemplate.getAppList(appListParam);
        String url = "";
        if (json != null && json.containsKey("data") && MyUtils.isNotEmpty(json.getJSONObject("data").getJSONArray("dataList"))) {
            url = json.getJSONObject("data").getJSONArray("dataList").getJSONObject(0).getString("applyUrl");
        }
        if (info == null) {
            return R.success(new JSONObject()
                    .fluentPut("url", url)
                    .fluentPut("precast", JSON.toJSONString(precast)));
        }
        boolean flag = false;
        JSONObject courseStatus = CourseApi.getCourseStatus(formRightBtnVO.getFid(), info.getKkxxb_kkxq(), info.getKkxxb_jxbbh());
        if (courseStatus != null && courseStatus.containsKey("data")) {
            JSONObject data = courseStatus.getJSONObject("data").getJSONObject("data");
            flag = data.values().stream().anyMatch(value -> value.equals(1));
        }
        return R.success(new JSONObject()
                .fluentPut("url", url)
                .fluentPut("precast", JSON.toJSONString(precast))
                .fluentPut("flag", flag ? 1 : 2));
    }

    /**
     * @param major 专业
     * @param grade 年级
     * @param fid   单位id
     * @description 根据专业和年级获取班级列表
     * <AUTHOR>
     * @date 2024/11/13 16:20
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @Override
    public R getClassByMajorAndGrade(String major, String grade, Integer fid) {
        if (StringUtils.isBlank(major) || StringUtils.isBlank(grade)) {
            return R.success();
        }
        return R.success(FormUtils.listAll(SearchStrBodyType.AND, fid, ClassInfoForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(ClassInfoForm::getBjxx_zy).match(major.split(","));
            searchStrBody.createAndAdd(ClassInfoForm::getBjxx_rxnf).eq(grade);
        }, ClassInfoForm.class));
    }

    /**
     * @param fid    单位id
     * @param formId 表单id
     * @description 获取培养方案打印模板
     * <AUTHOR>
     * @date 2024/12/5 16:30
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    @Override
    public JSONObject getTrainingProgramTpl(Integer fid, Integer formId) {
        return PrintApi.getTemplateList(fid, formId);
    }

    /**
     * @param info  筛选条件
     * @param page  当前页
     * @param limit 每页显示条数
     * @description 获取配课数据
     * <AUTHOR>
     * @date 2024/12/5 16:30
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    @Override
    public R getCourseAllocationData(CourseAllocationResultForm info, int page, int limit) {
        int fid = UserUtils.fid().intValue();
        SearchParam searchParam = SearchParam.builder()
                .deptId(fid)
                .appName(CourseAllocationResultForm.ALIAS)
                .cpage(page)
                .pageSize(limit)
                .build();
        SearchStrBody searchStrBody = SearchStrBody.and();
        if (StringUtils.isNotBlank(info.getPk_kkxq())) {
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_kkxq).match(info.getPk_kkxq().split(","));
        }
        if (StringUtils.isNotBlank(info.getPk_kcmc())) {
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_kcmc).match(info.getPk_kcmc().split(","));
        }
        if (StringUtils.isNotBlank(info.getPk_jxbmc())) {
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_jxbmc).match(info.getPk_jxbmc().split(","));
        }
        if (StringUtils.isNotBlank(info.getPk_pkzt())) {
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_pkzt).match(info.getPk_pkzt().split(","));
        }
        if (StringUtils.isNotBlank(info.getPk_skjs())) {
            List<TeacherInfoForm> teachers = FormUtils.listAll(SearchStrBodyType.OR, fid, TeacherInfoForm.ALIAS, strBody -> {
                for (String teacher : info.getPk_skjs().split(",")) {
                    strBody.createAndAdd(TeacherInfoForm::getJsjbxx_xm).eq(teacher);
                }
            }, TeacherInfoForm.class);
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_skjslxr).match(
                    teachers.stream()
                            .map(TeacherInfoForm::getJsjbxx_xmlxr)
                            .filter(Objects::nonNull)
                            .map(IdName::getPuid)
                            .filter(Objects::nonNull)
                            .mapToInt(Integer::intValue)
                            .toArray());
        }
        if (StringUtils.isNotBlank(info.getPk_jxbzc())) {
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_jxbzc).match(info.getPk_jxbzc().split(","));
        }
        if (StringUtils.isNotBlank(info.getPk_sfbzyrw())) {
            searchStrBody.createAndAdd(CourseAllocationResultForm::getPk_sfbzyrw).eq(info.getPk_sfbzyrw());
        }
        return R.success(FormTemplate.searchPaged(searchParam, searchStrBody, CourseAllocationResultForm.class));
    }

    /**
     * @param formUserId 专业教学计划数据id
     * @description 删除专业教学计划前置校验
     * <AUTHOR>
     * @date 2025/6/24 19:28
     * @retrun java.lang.Boolean
     **/
    @Override
    public Boolean deleteMajorTeachPlanValidate(String formUserId) {
        if (StringUtils.isBlank(formUserId)) {
            return false;
        }

        int fid = UserUtils.fid().intValue();
        MajorTeachPlanForm info = FormUtils.getById(fid, MajorTeachPlanForm.ALIAS, formUserId, MajorTeachPlanForm.class);
        if (info == null) {
            return false;
        }
        return FormUtils.count(SearchStrBodyType.AND, fid, TeachPlanForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_xnxq).eq(info.getTjzyjxjh_xq());
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_nj).eq(info.getTjzyjxjh_nj());
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_zybh).eq(info.getTjzyjxjh_bh());
        }) > 0;
    }

    /**
     * @param formUserId 专业教学计划数据id
     * @description 删除专业教学计划
     * <AUTHOR>
     * @date 2025/6/24 20:15
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @Override
    public R deleteMajorTeachPlan(String formUserId) {
        if (StringUtils.isBlank(formUserId)) {
            return R.fail("专业教学计划ID不能为空");
        }

        int fid = UserUtils.fid().intValue();
        FormUtils.batchDelete(SearchStrBodyType.AND, batchDeleteParam -> {
            batchDeleteParam.setFid(fid);
            batchDeleteParam.setFormUserIds(formUserId);
            batchDeleteParam.setFormAlias(MajorTeachPlanForm.ALIAS);
        }, null);

        // 查询要删除的教学计划列表
        List<CultivationMaintainTeachPlan> planList = maintainTeachPlanService
                .lambdaQuery()
                .eq(CultivationMaintainTeachPlan::getFid, fid)
                .eq(CultivationMaintainTeachPlan::getFormUserId, formUserId)
                .list();

        if (planList.isEmpty()) {
            return R.fail("未找到要删除的教学计划");
        }
        String idList = planList.stream()
                .map(plan -> String.valueOf(plan.getId()))
                .collect(Collectors.joining(","));
        return maintainTeachPlanService.batchDeleteMaintainTeachPlan(idList, formUserId);
    }

    /**
     * @param round 轮次
     * @description 天津机电根据轮次同步授课教师
     * <AUTHOR>
     * @date 2025/8/5 8:28
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    @Override
    public R roundSyncTeacher(String round) {
        // 参数验证
        if (StringUtils.isBlank(round)) {
            return R.fail("轮次参数不能为空");
        }

        // 异步执行同步任务
        ThreadTaskUtils.run(() -> {
            try {
                int fid = UserUtils.fid().intValue();
                log.info("开始执行轮次同步教师，fid: {}, round: {}", fid, round);

                // 1. 获取当前学期信息
                AcademicYearSemesterForm semester = SemesterUtils.current(fid);
                if (semester == null) {
                    log.error("未找到当前学期信息，fid: {}", fid);
                    return;
                }

                // 2. 获取轮次列表
                List<Xnxqlczbd> roundList = semester.getXnxqlczbd();
                if (MyUtils.isEmpty(roundList)) {
                    log.error("当前学期没有轮次配置，semester: {}", semester.getXnxq_xnxqh());
                    return;
                }

                // 3. 查找当前轮次
                Xnxqlczbd currentRound = findCurrentRound(roundList, round);
                if (currentRound == null) {
                    log.error("未找到指定轮次: {}", round);
                    return;
                }
                Xnxqlczbd previousRound = findPreviousRound(roundList, round);

                log.info("找到轮次信息 - 当前轮次: {}, 周次: {}, 上一轮次: {}, 周次: {}",
                    currentRound.getXnxq_lc(), currentRound.getXnxq_zc(),
                    previousRound != null ? previousRound.getXnxq_lc() : "无",
                    previousRound != null ? previousRound.getXnxq_zc() : "无");

                // 5. 获取当前轮次的课程信息
                List<CourseInformationForm> currentCourses = getCoursesByWeeks(fid, semester.getXnxq_xnxqh(),
                    currentRound.getXnxq_zc());

                // 6. 获取上一轮次的课程信息
                List<CourseInformationForm> previousCourses = Collections.emptyList();
                if (previousRound != null) {
                    previousCourses = getCoursesByWeeks(fid, semester.getXnxq_xnxqh(),
                        previousRound.getXnxq_zc());
                }

                log.info("获取课程数据 - 当前轮次课程数: {}, 上一轮次课程数: {}",
                    currentCourses.size(), previousCourses.size());

                // 7. 执行教师信息同步
                int syncCount = syncTeacherInfo(currentCourses, previousCourses, currentRound, previousRound);

                log.info("轮次同步教师完成，同步课程数: {}", syncCount);

            } catch (Exception e) {
                log.error("轮次同步教师异常", e);
            }
        });

        return R.success("同步任务已启动，请稍后查看结果");
    }

    /**
     * @param teachPlanForm 教学计划详情
     * @param operateType   1 修改教学计划 2 停用教学计划
     * @description 填充默认数据
     * <AUTHOR>
     * @date 2024/10/23 17:32
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    private JSONObject getTeachPlanChangePrecast(TeachPlanForm teachPlanForm, Integer operateType) {
        if (teachPlanForm == null) {
            return new JSONObject();
        }
        List<DataToFromJsonVo> dataToFromJsonVos = new ArrayList<>();
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_bglx", operateType == 1 ? "修改" : "停用"));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_kkxq", teachPlanForm.getJxjhgl_xnxq()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_nj", teachPlanForm.getJxjhgl_nj()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_zy", teachPlanForm.getJxjhgl_zy()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_zybh", teachPlanForm.getJxjhgl_zybh()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_pycc", teachPlanForm.getJxjhgl_pycc()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_skxb", teachPlanForm.getJxjhgl_ssxb()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_zyfx", teachPlanForm.getJxjhgl_zyfx()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_zydl", teachPlanForm.getJxjhgl_zydl()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_kcmc", teachPlanForm.getJxjhgl_kcmc()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_kcbh", teachPlanForm.getJxjhgl_kcid()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_kkbb", teachPlanForm.getJxjhgl_kkxb()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_kcssjys", teachPlanForm.getJxjhgl_kkjys()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_xf",
                Optional.ofNullable(teachPlanForm.getJxjhgl_xf()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_mzxs", teachPlanForm.getJxjhgl_mzxs()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.dan_hang_shu_ru, "jxjhbg_zssap", teachPlanForm.getJxjhgl_zxsap()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_sfcsjhj", teachPlanForm.getJxjhgl_sfcsjhj()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_sjzs",
                Optional.ofNullable(teachPlanForm.getJxjhgl_sjzs()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_sjxs",
                Optional.ofNullable(teachPlanForm.getJxjhgl_sjxs()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_llxs",
                Optional.ofNullable(teachPlanForm.getJxjhgl_llxs()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_syxs",
                Optional.ofNullable(teachPlanForm.getJxjhgl_syxs()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_sjixs",
                Optional.ofNullable(teachPlanForm.getJxjhgl_sjxss()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.shu_zi_shu_ru, "jxjhbg_qtxs",
                Optional.ofNullable(teachPlanForm.getJxjhgl_qtxs()).map(String::valueOf).orElse(null)));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_kcxz", teachPlanForm.getJxjhgl_kcxz()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_xbx", teachPlanForm.getJxjhgl_xbx()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.xia_la_kuang, "jxjhbg_ksxs", teachPlanForm.getJxjhgl_ksxs()));
        dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.dan_hang_shu_ru, "jxjhbg_jxjhglhid",
                String.valueOf(teachPlanForm.getRowInfo().getFormUserId())));
        if (MyUtils.isNotEmpty(teachPlanForm.getJxjhkc_jc())) {
            List<List<DataToFromJsonVo>> list = new ArrayList<>();
            for (Jxjhkc_jc info : teachPlanForm.getJxjhkc_jc()) {
                List<DataToFromJsonVo> rows = new ArrayList<>();
                rows.add(DataToFromJsonVo.add(FormComponentConstants.dan_hang_shu_ru, "jxjhbg_jcmc", info.getJxjhkc_jcmc()));
                rows.add(DataToFromJsonVo.add(FormComponentConstants.dan_hang_shu_ru, "jxjhbg_jch", info.getJxjhkc_jch()));
                rows.add(DataToFromJsonVo.add(FormComponentConstants.dan_hang_shu_ru, "jxjhbg_cbhisbn", info.getJxjhkc_isbn()));
                list.add(rows);
            }
            dataToFromJsonVos.add(DataToFromJsonVo.add(FormComponentConstants.zi_biao_dan, "jxjhbg_xyjc", list));
        }
        return FormUtils.dataToFromFrontJsonData(dataToFromJsonVos);
    }


    private void setCellStyle(ExcelWriter writer, int x, int y, CellStyle cellStyle, String content, int height) {
        int h = StringUtils.isNotBlank(content) && x > 0 && y > 1 ? -1 : height;
        if (StringUtils.isNotBlank(content)) {
            writer.writeCellValue(x, y, content);
        }
        writer.setRowHeight(y, h);
        writer.setColumnWidth(x, 30);
        if (cellStyle != null) {
            writer.setStyle(cellStyle, x, y);
        }
    }

    /**
     * 绘制单元格对角线
     *
     * @param writer Excel 写入器 {@link ExcelWriter}
     */
    private static void writeSecHeadRow(ExcelWriter writer) {
        // 合并需要绘制对角线的单元格
        // firstRow – 起始行，0开始
        // lastRow – 结束行，0开始
        // firstColumn – 起始列，0开始
        // lastColumn – 结束列，0开始
        // content – 合并单元格后的内容
        // isSetHeaderStyle – 是否为合并后的单元格设置默认标题样式，只提取边框样式
        Sheet sheet = writer.getSheet();
        HSSFPatriarch drawing = (HSSFPatriarch) sheet.createDrawingPatriarch();
        HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 1023, 255, (short) 0, 1, (short) 0, 1);
        HSSFSimpleShape shape = drawing.createSimpleShape(anchor);
        // 设置图形的类型为线
        shape.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE);
        // 设置边框线型：solid=0、dot=1、dash=2、lgDash=3、dashDot=4、lgDashDot=5、lgDashDotDot=6、sysDash=7、sysDot=8、sysDashDot=9、sysDashDotDot=10
        shape.setLineStyle(HSSFShape.LINESTYLE_SOLID);
    }

    private void teacherDataHandle(String filePath, JSONObject formJson, List<CourseInformationDto> allList) {
        try (ExcelWriter writer = ExcelUtil.getWriter(filePath)) {
            StyleSet styleSet = writer.getStyleSet();
            CellStyle cellStyleForNumber = styleSet.getCellStyleForNumber();
            cellStyleForNumber.setDataFormat(writer.getWorkbook().createDataFormat().getFormat("#,##0_ "));
            writer.setOnlyAlias(true);
            //移除默认sheet
            writer.getSheet().getWorkbook().removeSheetAt(0);
            //明细表（授课老师）处理
            List<CourseInformationDto> list1 = teacherSheet1(writer, allList, "明细表（授课老师）", formJson, "kkxxb_skjsxm", "kkxxb_skjsgh", "kkxxb_kcmc", "kkxxb_jxbzc", "kkxxb_jsmc", "kkxxb_zhouxs");
            //汇总表（授课老师）处理
            List<CourseInformationDto> list2 = teacherSheet2(writer, allList, "汇总表（授课老师）", formJson, "kkxxb_skjsxm", "kkxxb_skjsgh", "kkxxb_zhouxs");
            //明细表（助教）处理
            List<CourseInformationDto> list3 = teacherSheet3(writer, allList, formJson, "kkxxb_zjxm", "kkxxb_zjjsgh", "kkxxb_kcmc", "kkxxb_jxbzc", "kkxxb_jsmc", "kkxxb_zhouxs");
            //汇总表（助教）处理
            List<CourseInformationDto> list4 = teacherSheet4(writer, allList, formJson, "kkxxb_zjxm", "kkxxb_zjjsgh", "kkxxb_zhouxs");
            //明细表（授课教师+助教）
            list1.addAll(list3);
            teacherSheet1(writer, list1, "明细表（授课教师+助教）", formJson, "kkxxb_skjsxm", "kkxxb_skjsgh", "kkxxb_kcmc", "kkxxb_jxbzc", "kkxxb_jsmc", "kkxxb_zhouxs");
            //汇总表（授课教师+助教）
            list2.addAll(list4);
            teacherSheet2(writer, list2, "汇总表（授课教师+助教）", formJson, "kkxxb_skjsxm", "kkxxb_skjsgh", "kkxxb_zhouxs");

        } catch (Exception e) {
            log.error("导出教师开课信息统计异常->", e);
        }
    }

    private void clazzDataHandle(String filePath, JSONObject formJson, List<CourseInformationDto> allList) {
        try (ExcelWriter writer = ExcelUtil.getWriter(filePath)) {
            StyleSet styleSet = writer.getStyleSet();
            CellStyle cellStyleForNumber = styleSet.getCellStyleForNumber();
            cellStyleForNumber.setDataFormat(writer.getWorkbook().createDataFormat().getFormat("#,##0_ "));
            List<CourseInformationDto> dataList = new ArrayList<>();
            for (CourseInformationDto courseInformationForm : allList) {
                for (String s : courseInformationForm.getTeachClazzCompose()) {
                    CourseInformationDto newCourseInformationForm = new CourseInformationDto();
                    // 将原对象的属性复制给新对象
                    BeanUtil.copyProperties(courseInformationForm, newCourseInformationForm);
                    // 设置新对象的 Kkxxb_fybjbh 属性
                    newCourseInformationForm.setClazzName(s);
                    dataList.add(newCourseInformationForm);
                }
            }
            dataList.sort(Comparator.comparing(CourseInformationDto::getClazzName));
            AtomicInteger index = new AtomicInteger(1);
            dataList.forEach(item -> item.setId(index.getAndIncrement()));
            writer.setOnlyAlias(true);
            //移除默认sheet
            writer.getSheet().getWorkbook().removeSheetAt(0);

            //sheet1处理
            writer.addHeaderAlias("id", "序号");
            writer.addHeaderAlias("clazzName", "上课班级");
            writer.addHeaderAlias("courseName", formJson.getString("kkxxb_kcmc"));
            writer.addHeaderAlias("teachClazzCompose", formJson.getString("kkxxb_jxbzc"));
            writer.addHeaderAlias("teacherName", formJson.getString("kkxxb_skjsxm"));
            writer.addHeaderAlias("teacherNo", formJson.getString("kkxxb_skjsgh"));
            writer.addHeaderAlias("classRoom", formJson.getString("kkxxb_jsmc"));
            writer.addHeaderAlias("weekClassHour", formJson.getString("kkxxb_zhouxs"));
            writer.addHeaderAlias("totalClassHour", "学期总课时");
            writer.setSheet("明细表");
            writer.write(dataList);
            writer.close();

            //sheet2处理
            writer.clearHeaderAlias();
            writer.addHeaderAlias("id", "序号");
            writer.addHeaderAlias("clazzName", "上课班级");
            writer.addHeaderAlias("weekClassHour", formJson.getString("kkxxb_zhouxs"));
            writer.addHeaderAlias("totalClassHour", "学期总课时");
            writer.setSheet("汇总统计表");
            List<CourseInformationDto> sumDataList = new ArrayList<>();
            Map<String, Map<String, Double>> result = dataList.stream()
                    .collect(Collectors.groupingBy(CourseInformationDto::getClazzName, Collectors.collectingAndThen(Collectors.toList(), list -> {
                        double wxs = list.stream().mapToDouble(CourseInformationDto::getWeekClassHour).sum();
                        double zxs = list.stream().mapToDouble(CourseInformationDto::getTotalClassHour).sum();
                        return Collections.unmodifiableMap(new HashMap<String, Double>() {{
                            put("wxs", wxs);
                            put("zxs", zxs);
                        }});
                    })));
            List<Map.Entry<String, Map<String, Double>>> list = new ArrayList<>(result.entrySet());
            // 对嵌套的 Map 进行按照 kkxxb_skjsgh 属性降序排列
            list.sort(Map.Entry.comparingByKey());
            // 遍历排序后的结果
            int number = 0;
            for (Map.Entry<String, Map<String, Double>> entry : list) {
                number++;
                String key = entry.getKey();
                Map<String, Double> value = entry.getValue();
                sumDataList.add(new CourseInformationDto().setId(number).setClazzName(key)
                        .setWeekClassHour(value.get("wxs")).setTotalClassHour(value.get("zxs")));
            }
            writer.write(sumDataList);
            writer.flush();
        } catch (Exception e) {
            log.error("导出班级开课信息统计异常->", e);
        }
    }

    private List<CourseInformationDto> teacherSheet1(ExcelWriter writer, List<CourseInformationDto> allList, String sheetName, JSONObject formJson, String... title) {
        //sheet1处理
        writer.clearHeaderAlias();
        writer.addHeaderAlias("id", "序号");
        writer.addHeaderAlias("teacherName", formJson.getString(title[0]));
        writer.addHeaderAlias("teacherNo", formJson.getString(title[1]));
        writer.addHeaderAlias("courseName", formJson.getString(title[2]));
        writer.addHeaderAlias("teachClazzCompose", formJson.getString(title[3]));
        writer.addHeaderAlias("classRoom", formJson.getString(title[4]));
        writer.addHeaderAlias("weekClassHour", formJson.getString(title[5]));
        writer.addHeaderAlias("totalClassHour", "学期总课时");
        writer.setSheet(sheetName);
        writer.write(allList);
        writer.flush();
        return allList;
    }

    private List<CourseInformationDto> teacherSheet2(ExcelWriter writer, List<CourseInformationDto> allList, String sheetName, JSONObject formJson, String... title) {
        //sheet2处理
        writer.clearHeaderAlias();
        writer.addHeaderAlias("id", "序号");
        writer.addHeaderAlias("teacherName", formJson.getString(title[0]));
        writer.addHeaderAlias("teacherNo", formJson.getString(title[1]));
        writer.addHeaderAlias("weekClassHour", formJson.getString(title[2]));
        writer.addHeaderAlias("totalClassHour", "学期总课时");
        writer.setSheet(sheetName);
        List<CourseInformationDto> dataList = new ArrayList<>();
        Map<String, Map<String, Double>> result = allList.stream().filter(item -> StrUtil.isAllNotEmpty(item.getTeacherNo(), item.getTeacherName()))
                .collect(Collectors.groupingBy(item -> item.getTeacherNo() + "-" + item.getTeacherName(), Collectors.collectingAndThen(Collectors.toList(), list -> {
                    double wxs = list.stream().mapToDouble(CourseInformationDto::getWeekClassHour).sum();
                    double zxs = list.stream().mapToDouble(CourseInformationDto::getTotalClassHour).sum();
                    return Collections.unmodifiableMap(new HashMap<String, Double>() {{
                        put("wxs", wxs);
                        put("zxs", zxs);
                    }});
                })));
        List<Map.Entry<String, Map<String, Double>>> list = new ArrayList<>(result.entrySet());
        // 对嵌套的 Map 进行按照 kkxxb_skjsgh 属性降序排列
        list.sort(Comparator.comparing(Map.Entry::getKey));
        // 遍历排序后的结果
        int number = 0;
        for (Map.Entry<String, Map<String, Double>> entry : list) {
            number++;
            String key = entry.getKey();
            String teacherNo = key.split("-")[0];
            String teacher = key.split("-")[1];
            Map<String, Double> value = entry.getValue();
            dataList.add(new CourseInformationDto().setId(number).setTeacherNo(teacherNo).setTeacherName(teacher)
                    .setWeekClassHour(value.get("wxs")).setTotalClassHour(value.get("zxs")));
        }
        writer.write(dataList);
        writer.flush();
        return dataList;
    }

    private List<CourseInformationDto> teacherSheet3(ExcelWriter writer, List<CourseInformationDto> allList, JSONObject formJson, String... title) {
        //sheet3处理
        writer.clearHeaderAlias();
        List<CourseInformationDto> dataList = assistantData(allList);
        teacherSheet1(writer, dataList, "明细表（助教）", formJson, title);
        return dataList;
    }

    private List<CourseInformationDto> teacherSheet4(ExcelWriter writer, List<CourseInformationDto> allList, JSONObject formJson, String... title) {
        //sheet4处理
        writer.clearHeaderAlias();
        List<CourseInformationDto> dataList = assistantData(allList);
        teacherSheet2(writer, dataList, "汇总表（助教）", formJson, title);
        return dataList;
    }

    private List<CourseInformationDto> assistantData(List<CourseInformationDto> allList) {
        List<CourseInformationDto> dataList = new ArrayList<>();
        int number = 0;
        for (CourseInformationDto courseInformationDto : allList) {
            if (StringUtils.isNotBlank(courseInformationDto.getAssistantNo()) && StringUtils.isNotBlank(courseInformationDto.getAssistantName())) {
                String[] teacherNo = courseInformationDto.getAssistantNo().split(",");
                String[] teacherName = courseInformationDto.getAssistantName().split(",");
                for (int i = 0; i < teacherNo.length; i++) {
                    number++;
                    CourseInformationDto newCourseInformationForm = new CourseInformationDto();
                    BeanUtil.copyProperties(courseInformationDto, newCourseInformationForm);
                    newCourseInformationForm.setId(number);
                    newCourseInformationForm.setTeacherNo(teacherNo[i]);
                    newCourseInformationForm.setTeacherName(teacherName[i]);
                    dataList.add(newCourseInformationForm);
                }
            }
        }
        return dataList;
    }

    private void exportStatisticsExcel(Integer fid, Integer dataType, String semester, List<CourseInformationDto> allList, JSONObject formJson, String filePath) {
        //查询课程库符合统计的课程编号列表
        List<CourseLibraryForm> courseList = FormUtils.listAll(SearchStrBodyType.AND, fid, CourseLibraryForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(CourseLibraryForm::getKck_sfcykktj).eq(1);
        }, CourseLibraryForm.class);
        List<String> courseIdList = courseList.stream().map(CourseLibraryForm::getKck_kcbh).collect(Collectors.toList());
        if (MyUtils.isEmpty(courseIdList)) {
            return;
        }
        //统计开课信息表符合条件的数据
        FormUtils.pagesIterator(SearchStrBodyType.AND, fid, CourseInformationForm.ALIAS,
                searchStrBody -> {
                    searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kcbh).match(ArrayUtil.toArray(courseIdList, String.class));
                    searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kkxq).match(semester.split(","));
                    if (dataType == 1) {
                        searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_skjsxm).isNotNull();
                        searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_skjsgh).isNotNull();
                    } else if (dataType == 2) {
                        searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_jxbzclx).match("纯行政班", "行政班合班");
                    }
                },
                list -> {
                    for (CourseInformationForm courseInformationForm : list) {
                        List<IdName> teachers = courseInformationForm.getKkxxb_skjsxm();
                        List<IdName> aTeacher = courseInformationForm.getKkxxb_zjxm();
                        if (CollUtil.isNotEmpty(teachers)) {
                            allList.addAll(teachers.parallelStream().map(teacher -> {
                                CourseInformationDto courseInformationDto = new CourseInformationDto();
                                BeanUtil.copyProperties(courseInformationForm, courseInformationDto);
                                courseInformationDto.setTeacherName(teacher.getUname());
                                courseInformationDto.setTeachClazzCompose(courseInformationForm.getKkxxb_jxbzc());
                                courseInformationDto.setCourseName(courseInformationForm.getKkxxb_kcmc());
                                courseInformationDto.setClassRoom(courseInformationForm.getKkxxb_jsmc());
                                int index = teachers.indexOf(teacher);
                                if (StringUtils.isNotBlank(courseInformationForm.getKkxxb_skjsgh())) {
                                    courseInformationDto.setTeacherNo(ArrayUtil.get(courseInformationForm.getKkxxb_skjsgh().split(","), index));
                                }
                                Double week = Double.parseDouble(StrUtil.emptyToDefault(courseInformationForm.getKkxxb_zhouxs(), "0"));
                                Double weekCount = Double.parseDouble(StrUtil.emptyToDefault(courseInformationForm.getKkxxb_jxzs(), "0"));
                                courseInformationDto.setTotalClassHour(week + weekCount);
                                courseInformationDto.setWeekClassHour(week);

                                if (CollUtil.isNotEmpty(aTeacher)) {
                                    courseInformationDto.setAssistantNo(StringUtils.isNotBlank(courseInformationForm.getKkxxb_zjjsgh()) ? courseInformationForm.getKkxxb_zjjsgh() : "");
                                    courseInformationDto.setAssistantName(aTeacher.stream().map(IdName::getUname).collect(Collectors.joining(", ")));
                                }
                                return courseInformationDto;
                            }).collect(Collectors.toList()));
                        }
                    }
                }, CourseInformationForm.class);
        if (dataType == 1) {
            //教师开课信息统计
            allList.sort(Comparator.comparing(item -> item.getTeacherNo() + "-" + item.getTeacherName()));
            AtomicInteger index = new AtomicInteger(1);
            allList.forEach(item -> item.setId(index.getAndIncrement()));
            teacherDataHandle(filePath, formJson, allList);
        } else if (dataType == 2) {
            //班级开课信息统计
            clazzDataHandle(filePath, formJson, allList);
        }
    }

    private void exportEquipmentExcel(Integer fid, String semester, String filePath) {
        Pattern PATTERN = Pattern.compile("（(\\d+)）");
        try (ExcelWriter writer = ExcelUtil.getWriter(filePath)) {
            JSONObject schoolInfo = UserApi.getSchoolInfo(fid);
            String schoolName = MyUtils.isNotEmpty(schoolInfo) && schoolInfo.containsKey("name") ? schoolInfo.getString("name") : "";
            CellStyle bodyCellStyle = writer.getStyleSet().getCellStyle();
            bodyCellStyle.setWrapText(true);
            CellStyle style = writer.getHeadCellStyle();
            style.setWrapText(true);
            // 创建字体
            Font font = writer.createFont();
            font.setFontHeightInPoints((short) 14);
            // 获取标题样式并设置字体
            style.setFont(font);
            style.setBorderRight(writer.getCellStyle().getBorderRight());
            style.setBorderBottom(writer.getCellStyle().getBorderBottom());
            List<CourseLibraryForm> courseList = FormUtils.listAll(SearchStrBodyType.AND, searchParamConsumer -> {
                searchParamConsumer.setDeptId(fid);
                searchParamConsumer.setAppName(CourseLibraryForm.ALIAS);
                searchParamConsumer.setOrderType("asc");
                searchParamConsumer.setOrderBy("jxpbb");
            }, searchStrBody -> searchStrBody.createAndAdd(CourseLibraryForm::getJxpbb).isNotNull(), CourseLibraryForm.class);
            if (MyUtils.isEmpty(courseList)) {
                return;
            }
            List<String> courseIds = CollUtil.getFieldValues(courseList, "kck_kcbh", String.class);
            List<CourseInformationForm> all = FormUtils.listAll(SearchStrBodyType.AND, fid, CourseInformationForm.ALIAS, searchStrBody -> {
                searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_byzd1).match(semester.split(","));
                searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_jxbzc).isNotNull();
            }, CourseInformationForm.class);
            List<CourseInformationForm> list = all.stream()
                    .filter(course -> Objects.equals(course.getKkxxb_jxbzclx(), "纯行政班"))
                    .collect(Collectors.toList());
            List<ClassInfoForm> classList = FormUtils.listAll(SearchStrBodyType.AND, fid, ClassInfoForm.ALIAS, null, ClassInfoForm.class);
            Map<String, List<CourseInformationForm>> groupedAndSorted = list.stream()
                    .filter(course -> course.getKkxxb_njnj() != null)
                    .sorted(Comparator.comparing(course -> Integer.parseInt(course.getKkxxb_njnj())))
                    .collect(Collectors.groupingBy(CourseInformationForm::getKkxxb_njnj, LinkedHashMap::new, Collectors.toList()));
            writer.getSheet().getWorkbook().removeSheetAt(0);
            groupedAndSorted.forEach((key, grade) -> {
                writer.setSheet(key);
                grade.sort(Comparator.comparingInt(form -> {
                    Matcher m = PATTERN.matcher(form.getKkxxb_jxbzc().get(0));
                    if (m.find()) {
                        return Integer.parseInt(m.group(1));
                    }
                    return Integer.parseInt(form.getKkxxb_jxbzc().get(0));
                }));
                Map<String, List<CourseInformationForm>> map = grade.stream().collect(Collectors.groupingBy(form -> StrUtil.join(",", form.getKkxxb_jxbzc()), LinkedHashMap::new, Collectors.toList()));
                List<Map<String, Object>> rows = new ArrayList<>();
                List<String> courseNames = new ArrayList<>();
                int dataSize = 0;
                for (Map.Entry<String, List<CourseInformationForm>> entry : map.entrySet()) {
                    List<CourseInformationForm> clazz = entry.getValue();
                    CourseInformationForm cif = clazz.get(0);
                    List<ClassInfoForm> classInfoForm = classList.stream().filter(form -> ObjectUtil.equal(form.getBjxx_bjbh(), cif.getKkxxb_jxbzcbh())).collect(Collectors.toList());
                    String classTeacher = MyUtils.isNotEmpty(classInfoForm) ? classInfoForm.get(0).getBjxx_bzr() : "";
                    String major = MyUtils.isNotEmpty(classInfoForm) ? classInfoForm.get(0).getBjxx_zy() : "";
                    int weekCount = clazz.stream().filter(form -> ObjectUtil.isNotEmpty(form.getKkxxb_zhouxs())).mapToInt(form -> Integer.parseInt(form.getKkxxb_zhouxs())).sum();
                    long courseCount = all.stream().filter(form -> ObjectUtil.equal(form.getKkxxb_sfxk(), FormUtils.YES) && ObjectUtil.equal(form.getKkxxb_kkxq(), cif.getKkxxb_kkxq()) && ObjectUtil.contains(StrUtil.join(",", form.getKkxxb_jxbzc()), StrUtil.join(",", cif.getKkxxb_jxbzc()))).count();
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("班级", StrUtil.join(",", cif.getKkxxb_jxbzc()));
                    row.put("专业", major);
                    row.put("课时", weekCount);
                    row.put("选修课", courseCount);
                    row.put("班主任", classTeacher);
                    for (int i = 0; i < courseList.size(); i++) {
                        int finalI = i;
                        List<String> finalCourseNames = courseNames;
                        clazz.stream().filter(form -> ObjectUtil.equal(form.getKkxxb_kcbh(), courseIds.get(finalI))).forEach(courseInformationForm -> {
                            String teacher = CollUtil.join(CollUtil.getFieldValues(courseInformationForm.getKkxxb_skjsxm(), "uname", String.class), "/");
                            finalCourseNames.add(courseInformationForm.getKkxxb_kcmc());
                            row.put("teacher" + finalI, teacher);
                            row.put("courseHour" + finalI, courseInformationForm.getKkxxb_zhouxs());
                        });
                    }
                    int noFixedCount = 0;
                    clazz.sort(Comparator.comparing(CourseInformationForm::getKkxxb_kcmc));
                    for (CourseInformationForm courseInformationForm : clazz) {
                        String teacher = CollUtil.join(CollUtil.getFieldValues(courseInformationForm.getKkxxb_skjsxm(), "uname", String.class), "/");
                        if (ObjectUtil.contains(StrUtil.join(",", courseIds), courseInformationForm.getKkxxb_kcbh())) {
                            continue;
                        }
                        noFixedCount++;
                        dataSize = Math.max(dataSize, noFixedCount);
                        row.put("noFixedTeacher" + noFixedCount, courseInformationForm.getKkxxb_kcmc() + "/" + teacher);
                        row.put("noFixedCourseHour" + noFixedCount, courseInformationForm.getKkxxb_zhouxs());
                    }
                    rows.add(row);
                }
                rows = completeMissingKeys(rows, courseList.size(), dataSize);
                List<String> sourceCourseNames = CollUtil.getFieldValues(courseList, "kck_kcmc", String.class);
                courseNames = courseNames.stream().distinct().sorted(Comparator.comparingInt(sourceCourseNames::indexOf)).collect(Collectors.toList());
                String term = grade.get(0).getKkxxb_byzd1() + grade.get(0).getKkxxb_njnj() + "级";
                writer.merge(courseNames.size() * 2 + 4 + dataSize * 2, schoolName + term + "各班班主任及任课老师配备表                      教研组长：");
                writer.setRowHeight(0, 40);
                writer.write(rows, true);
                writer.setRowHeight(1, 40);
                int firstColumn = 5;
                for (String course : courseNames) {
                    writer.merge(1, 1, firstColumn, firstColumn + 1, course, true);
                    writer.setColumnWidth(firstColumn + 1, 3);
                    firstColumn = firstColumn + 2;
                }
                int lastColumn = firstColumn + dataSize;
                for (int i = firstColumn; i < lastColumn; i++) {
                    writer.merge(1, 1, firstColumn, firstColumn + 1, "", true);
                    writer.setColumnWidth(firstColumn + 1, 3);
                    firstColumn = firstColumn + 2;
                }
            });
        } catch (Exception e) {
            log.error("导出教学配备表异常->", e);
        }
    }

    private void exportCourseDivide(Integer fid, String semester, String filePath) {
        try (ExcelWriter writer = ExcelUtil.getWriter(filePath)) {
            List<CourseDivideForm> list = FormUtils.listAll(SearchStrBodyType.AND, fid, CourseDivideForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(CourseDivideForm::getKcfpb_xnxq).eq(semester), CourseDivideForm.class);
            List<List<CourseDivideForm>> subjectGroup = CollUtil.groupByField(list, "kcfpb_xkz");
            if (MyUtils.isEmpty(subjectGroup)) {
                return;
            }
            CellStyle style = writer.getHeadCellStyle();
            style.setWrapText(true);
            writer.getSheet().getWorkbook().removeSheetAt(0);
            for (List<CourseDivideForm> subject : subjectGroup) {
                List<Map<String, Object>> rows = new ArrayList<>();
                writer.setSheet(subject.get(0).getKcfpb_xkz());
                writer.merge(4, semester + "\t" + subject.get(0).getKcfpb_xkz() + "\t学科课程分配表");
                writer.setRowHeight(0, 40);
                writer.setColumnWidth(0, 40);
                writer.setColumnWidth(1, 40);
                writer.setColumnWidth(2, 40);
                writer.setColumnWidth(3, 40);
                writer.setColumnWidth(4, 40);
                for (CourseDivideForm courseDivideForm : subject) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("课程名称", courseDivideForm.getKcfpb_kcmc());
                    row.put("周课时数\n（算小班化）", courseDivideForm.getKcfpb_kssx());
                    row.put("周课时数", courseDivideForm.getKcfpb_kss());
                    row.put("周数", courseDivideForm.getKcfpb_zs());
                    row.put("班级", courseDivideForm.getKcfpb_bj());
                    rows.add(row);
                }
                writer.write(rows, true);
            }
        } catch (Exception e) {
            log.error("导出课程分配表异常->", e);
        }
    }

    private static List<Map<String, Object>> completeMissingKeys(List<Map<String, Object>> dataList, int len, int dataSize) {
        // 定义键的顺序
        List<String> keyOrder = new ArrayList<>();
        keyOrder.add("班级");
        keyOrder.add("专业");
        keyOrder.add("课时");
        keyOrder.add("选修课");
        keyOrder.add("班主任");
        for (int i = 0; i < len; i++) {
            keyOrder.add("teacher" + i);
            keyOrder.add("courseHour" + i);
        }
        for (int i = 0; i < dataSize + 1; i++) {
            keyOrder.add("noFixedTeacher" + i);
            keyOrder.add("noFixedCourseHour" + i);
        }
        // 补全缺失的key
        dataList.forEach(map -> keyOrder.forEach(key -> map.putIfAbsent(key, null)));
        // 移除所有值都为null的键
        List<String> keysToRemove = keyOrder.stream()
                .filter(key -> dataList.stream().allMatch(map -> map.get(key) == null))
                .collect(Collectors.toList());
        // 为了保持顺序，我们需要创建一个新的有序的Map
        return dataList.stream().map(map -> {
            Map<String, Object> orderedMap = new LinkedHashMap<>();
            keyOrder.forEach(key -> {
                if (!keysToRemove.contains(key)) {
                    orderedMap.put(key, map.get(key));
                }
            });
            return orderedMap;
        }).collect(Collectors.toList());
    }

    private JSONArray packageFormJson(String formField, String formFieldVal, List<Kksz> kkszList, List<IdName> teacherContacts, String formUserId, Integer syncScoreTeacher) {
        JSONArray childArray = new JSONArray();
        List<CultivationClassStartsInfoDetail> details = classStartsInfoDetailService.lambdaQuery().eq(CultivationClassStartsInfoDetail::getCsFormUserId, formUserId).eq(CultivationClassStartsInfoDetail::getStatus, 0).list();
        if ("teacher".equals(formField) || "scoreTeacher".equals(formField) || "assistant".equals(formField)) {
            if (MyUtils.isNotEmpty(details)) {
                CultivationClassStartsInfoDetail detail = details.get(0);
                IdName teacherContact = MyUtils.isNotEmpty(teacherContacts) ? teacherContacts.get(0) : null;
                String puid = teacherContact != null ? String.valueOf(teacherContact.getPuid()) : null;
                String uname = teacherContact != null ? teacherContact.getUname() : null;
                LambdaQueryWrapper<CultivationClassStartsTeacher> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(CultivationClassStartsTeacher::getDetailId, detail.getId()).last("limit 1");
                CultivationClassStartsTeacher teacher = classStartsTeacherService.getOne(wrapper);
                teacher = teacher == null ? new CultivationClassStartsTeacher() : teacher;
                if ("teacher".equals(formField)) {
                    teacher = StringUtils.isBlank(teacher.getScoreTeacher()) ? teacher.setScoreTeacherUid(puid).setScoreTeacher(uname) : teacher;
                    classStartsTeacherService.saveOrUpdate(teacher.setTeacherName(uname).setDetailId(detail.getId()).setTeacherUid(puid));
                } else if ("scoreTeacher".equals(formField)) {
                    classStartsTeacherService.saveOrUpdate(teacher.setDetailId(detail.getId()).setScoreTeacherUid(puid).setScoreTeacher(uname));
                } else {
                    classStartsTeacherService.saveOrUpdate(teacher.setDetailId(detail.getId()).setTeachAsistantUid(puid).setTeachAsistant(uname));
                }
            }
        }
        for (Kksz kksz : kkszList) {
            if ("teacher".equals(formField)) {
                kksz.setKkgl_skjs(teacherContacts);
                if (syncScoreTeacher == 1) {
                    kksz.setKkgl_cjlrjs(teacherContacts);
                }
            } else if ("assistant".equals(formField)) {
                kksz.setKkgl_zj(teacherContacts);
                if (syncScoreTeacher == 1) {
                    kksz.setKkgl_cjlrjs(teacherContacts);
                }
            } else if ("scoreTeacher".equals(formField)) {
                kksz.setKkgl_cjlrjs(teacherContacts);
            } else if ("kkgl_zc".equals(formField)) {
                kksz.setKkgl_zc(formFieldVal);
            } else if ("kkgl_zks".equals(formField)) {
                kksz.setKkgl_zks(StringUtils.isNotBlank(formFieldVal) ? formFieldVal : null);
            } else if ("kkgl_lpjc".equals(formField)) {
                kksz.setKkgl_lpjc(StringUtils.isNotBlank(formFieldVal) ? Integer.valueOf(formFieldVal) : null);
            } else if ("kkgl_sflcj".equals(formField)) {
                kksz.setKkgl_sflcj(formFieldVal);
            } else if ("kkgl_lpgz".equals(formField)) {
                kksz.setKkgl_lpgz(formFieldVal);
            } else if ("kkgl_jslx".equals(formField)) {
                kksz.setKkgl_jslx(formFieldVal);
            } else if ("kkgl_jsbh".equals(formField)) {
                kksz.setKkgl_jsbh(StringUtils.isNotBlank(formFieldVal) ? formFieldVal.split(",")[0] : "");
                kksz.setKkgl_jsmc(StringUtils.isNotBlank(formFieldVal) ? formFieldVal.split(",")[1] : "");
                if (MyUtils.isNotEmpty(details)) {
                    CultivationClassStartsInfoDetail detail = details.get(0);
                    LambdaQueryWrapper<CultivationClassStartsTeacher> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(CultivationClassStartsTeacher::getDetailId, detail.getId()).last("limit 1");
                    CultivationClassStartsTeacher teacher = classStartsTeacherService.getOne(wrapper);
                    teacher = teacher == null ? new CultivationClassStartsTeacher() : teacher;
                    classStartsTeacherService.saveOrUpdate(teacher.setAppointClassRoom(kksz.getKkgl_jsmc()).setDetailId(detail.getId()));
                }
            }
            String formDataStr = FormUtil.reflectFormData(kksz);
            JSONArray formData = JSON.parseArray(formDataStr);
            if (MyUtils.isEmpty(kksz.getKkgl_skjs())) {
                formData.add(new ComptIdValue("kkgl_skjs", FormComponentConstants.lian_xi_ren));
            }
            if (MyUtils.isEmpty(kksz.getKkgl_zj())) {
                formData.add(new ComptIdValue("kkgl_zj", FormComponentConstants.lian_xi_ren));
            }
            if (MyUtils.isEmpty(kksz.getKkgl_cjlrjs())) {
                formData.add(new ComptIdValue("kkgl_cjlrjs", FormComponentConstants.lian_xi_ren));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_zc())) {
                formData.add(new ComptIdValue("kkgl_zc", FormComponentConstants.dan_hang_shu_ru));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_zks())) {
                formData.add(new ComptIdValue("kkgl_zks", FormComponentConstants.shu_zi_shu_ru));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_lpjc())) {
                formData.add(new ComptIdValue("kkgl_lpjc", FormComponentConstants.shu_zi_shu_ru));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_sflcj())) {
                formData.add(new ComptIdValue("kkgl_sflcj", FormComponentConstants.dan_xuan));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_lpgz())) {
                formData.add(new ComptIdValue("kkgl_lpgz", FormComponentConstants.dan_hang_shu_ru));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_jslx())) {
                formData.add(new ComptIdValue("kkgl_jslx", FormComponentConstants.xia_la_kuang));
            }
            if (ObjectUtil.isEmpty(kksz.getKkgl_jsbh())) {
                formData.fluentAdd(new ComptIdValue("kkgl_jsbh", FormComponentConstants.xia_la_kuang)).fluentAdd(new ComptIdValue("kkgl_jsmc", FormComponentConstants.xia_la_kuang));
            }
            childArray.add(formData);
        }
        return childArray;
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @description 获取开课设置审批数据
     * <AUTHOR>
     * @date 2024/7/25 10:11
     * @retrun java.util.List<com.chaoxing.academic.entity.form.cultivation.CourseInformationForm>
     **/
    private JSONObject getCourseManageApproveData(FormRightBtnVO formRightBtnVO) {
        JSONObject json = new JSONObject();
        AprvIdSearchParam aprvIdSearchParam = AprvIdSearchParam.builder().deptId(formRightBtnVO.getFid()).appName(CourseManageApprove.ALIAS)
                .formUserIds(formRightBtnVO.getFormUserId()).build();
        List<CourseManageApprove> courseManageApproves = ApproveTemplate.searchById(aprvIdSearchParam, CourseManageApprove.class);
        if (MyUtils.isEmpty(courseManageApproves)) {
            return null;
        }
        CourseManageApprove courseManageApprove = courseManageApproves.get(0);
        json.put("term", courseManageApprove.getKkszsp_kkxnxq());
        if (MyUtils.isNotEmpty(courseManageApprove.getKkszsp_kksz())) {
            getCourseManageApproveTeacherData(json, courseManageApprove, formRightBtnVO);
        }
        if (MyUtils.isNotEmpty(courseManageApprove.getKkszsp_jxbzc())) {
            getCourseManageApproveClazzData(json, courseManageApprove, formRightBtnVO);
        }
        return json;
    }

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @description 获取开课设置数据
     * <AUTHOR>
     * @date 2024/7/25 10:11
     * @retrun java.util.List<com.chaoxing.academic.entity.form.cultivation.CourseInformationForm>
     **/
    private JSONObject getCourseManageData(FormRightBtnVO formRightBtnVO) {
        JSONObject json = new JSONObject();
        CourseManageForm courseManageForm = FormUtils.getById(formRightBtnVO.getFid(), CourseManageForm.ALIAS, formRightBtnVO.getFormUserId(), CourseManageForm.class);
        if (courseManageForm == null) {
            return null;
        }
        json.put("term", courseManageForm.getKkgl_kkxq());
        if (MyUtils.isNotEmpty(courseManageForm.getKksz())) {
            getCourseManageTeacherData(json, courseManageForm, formRightBtnVO);
        }
        if (MyUtils.isNotEmpty(courseManageForm.getKkgl_jxbzc())) {
            getCourseManageClazzData(json, courseManageForm, formRightBtnVO);
        }
        return json;
    }

    /**
     * @param json             结果数据
     * @param courseManageForm 开课信息详情
     * @param formRightBtnVO   右侧按钮参数对象
     * @description 获取授课教师开课总学时
     * <AUTHOR>
     * @date 2024/7/26 15:29
     * @retrun void
     **/
    private void getCourseManageTeacherData(JSONObject json, CourseManageForm courseManageForm, FormRightBtnVO formRightBtnVO) {
        List<String> teacherArray = courseManageForm.getKksz().stream()
                .filter(kksz -> MyUtils.isNotEmpty(kksz.getKkgl_skjs()))
                .flatMap(kksz -> kksz.getKkgl_skjs().stream().map(u -> String.valueOf(u.getPuid()))).distinct().collect(Collectors.toList());
        List<CourseManageForm> teacherList = FormUtils.listAll(SearchStrBodyType.AND, formRightBtnVO.getFid(), CourseManageForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(CourseManageForm::getKkgl_kkxq).eq(courseManageForm.getKkgl_kkxq());
            searchStrBody.createAndAdd(Kksz::getKkgl_skjs).match(teacherArray.toArray(new String[0]));
        }, CourseManageForm.class);
        Map<String, Integer> map = new HashMap<>();
        for (String uid : teacherArray) {
            TeacherInfoForm teacherInfo = FormUtils.getOne(SearchStrBodyType.AND, formRightBtnVO.getFid(), TeacherInfoForm.ALIAS, searchStrBody -> {
                searchStrBody.createAndAdd(TeacherInfoForm::getJsjbxx_uid).eq(uid);
            }, TeacherInfoForm.class);
            if (teacherInfo == null) {
                continue;
            }
            String key = teacherInfo.getJsjbxx_jsgh() + "|" + teacherInfo.getJsjbxx_xm();
            for (CourseManageForm manageForm : teacherList) {
                for (Kksz kksz : manageForm.getKksz()) {
                    if (MyUtils.isEmpty(kksz.getKkgl_skjs())) {
                        continue;
                    }
                    List<String> teacherUIds = kksz.getKkgl_skjs().stream().map(u -> String.valueOf(u.getPuid())).collect(Collectors.toList());
                    if (!teacherUIds.contains(uid)) {
                        continue;
                    }
                    calculateHours(Collections.singletonList(kksz), map, key);
                }
            }
        }
        json.put("teacherList", map);
    }

    /**
     * @param json             结果数据
     * @param courseManageForm 开课信息详情
     * @param formRightBtnVO   右侧按钮参数对象
     * @description 获取班级开课总学时
     * <AUTHOR>
     * @date 2024/7/26 15:28
     * @retrun void
     **/
    private void getCourseManageClazzData(JSONObject json, CourseManageForm courseManageForm, FormRightBtnVO formRightBtnVO) {
        SearchStrBody searchStrBody = SearchStrBody.and();
        searchStrBody.createAndAdd(CourseManageForm::getKkgl_kkxq).eq(courseManageForm.getKkgl_kkxq());
        searchStrBody.createAndAdd(CourseManageForm::getKkgl_jxbzc).match(courseManageForm.getKkgl_jxbzc().toArray(new String[0]));
        List<CourseManageForm> clazzList = FormUtils.getFormData(formRightBtnVO.getFid(), CourseManageForm.ALIAS, searchStrBody, CourseManageForm.class);
        if (MyUtils.isEmpty(clazzList)) {
            return;
        }
        List<String> teachClazzArray = courseManageForm.getKkgl_jxbzc();
        String[] teachClazzCodeArray = courseManageForm.getKkgl_jxbzcbh().split("[,，]");
        Map<String, Integer> map = new HashMap<>();
        for (CourseManageForm manageForm : clazzList) {
            if (MyUtils.isEmpty(manageForm.getKksz())) {
                continue;
            }
            for (int i = 0; i < teachClazzArray.size(); i++) {
                String teachClazz = teachClazzArray.get(i);
                String key = teachClazzCodeArray[i] + "|" + teachClazz;
                if (!manageForm.getKkgl_jxbzc().contains(teachClazz)) {
                    continue;
                }
                calculateHours(manageForm.getKksz(), map, key);
            }
        }
        json.put("clazzList", map);
    }

    /**
     * @param list 开课管理-开课设置信息
     * @param map  开课学时统计map
     * @param key  学时key
     * @description 计算开课学时
     * <AUTHOR>
     * @date 2024/7/26 11:03
     * @retrun void
     **/
    private void calculateHours(List<Kksz> list, Map<String, Integer> map, String key) {
        for (Kksz kksz : list) {
            if (StringUtils.isBlank(kksz.getKkgl_zks()) || StringUtils.isBlank(kksz.getKkgl_zc())) {
                continue;
            }
            int teachingWeeks = Arrays.stream(kksz.getKkgl_zc().split("[,，]")).mapToInt(range -> {
                String[] rangeParts = range.split("-");
                return Integer.parseInt(rangeParts[rangeParts.length - 1]) - Integer.parseInt(rangeParts[0]) + 1;
            }).sum();
            int totalHours = teachingWeeks * Integer.parseInt(kksz.getKkgl_zks());
            map.merge(key, totalHours, Integer::sum);
        }
    }

    /**
     * @param json                结果数据
     * @param courseManageApprove 开课审批信息详情
     * @param formRightBtnVO      右侧按钮参数对象
     * @description 获取授课教师开课总学时
     * <AUTHOR>
     * @date 2024/7/26 15:29
     * @retrun void
     **/
    private void getCourseManageApproveTeacherData(JSONObject json, CourseManageApprove courseManageApprove, FormRightBtnVO formRightBtnVO) {
        List<String> teacherArray = courseManageApprove.getKkszsp_kksz().stream()
                .filter(kksz -> MyUtils.isNotEmpty(kksz.getKkszsp_skjs()))
                .flatMap(kksz -> kksz.getKkszsp_skjs().stream().map(u -> String.valueOf(u.getPuid()))).distinct().collect(Collectors.toList());
        AprvSearchParam approveSearchParam = AprvSearchParam.builder().deptId(formRightBtnVO.getFid()).formAlias(CourseManageApprove.ALIAS).build();
        SearchStrBody strBody = SearchStrBody.and();
        strBody.createAndAdd(CourseManageApprove::getKkszsp_kkxnxq).match(courseManageApprove.getKkszsp_kkxnxq());
        strBody.createAndAdd(Kkszsp_kksz::getKkszsp_skjs).match(teacherArray.toArray(new String[0]));
        List<CourseManageApprove> teacherList = ApproveTemplate.search(approveSearchParam, strBody, CourseManageApprove.class);
        Map<String, Integer> map = new HashMap<>();
        for (String uid : teacherArray) {
            TeacherInfoForm teacherInfo = FormUtils.getOne(SearchStrBodyType.AND, formRightBtnVO.getFid(), TeacherInfoForm.ALIAS, searchStrBody -> {
                searchStrBody.createAndAdd(TeacherInfoForm::getJsjbxx_uid).eq(uid);
            }, TeacherInfoForm.class);
            if (teacherInfo == null) {
                continue;
            }
            String key = teacherInfo.getJsjbxx_jsgh() + "|" + teacherInfo.getJsjbxx_xm();
            for (CourseManageApprove approve : teacherList) {
                for (Kkszsp_kksz kksz : approve.getKkszsp_kksz()) {
                    if (MyUtils.isEmpty(kksz.getKkszsp_skjs())) {
                        continue;
                    }
                    List<String> teacherUIds = kksz.getKkszsp_skjs().stream().map(u -> String.valueOf(u.getPuid())).collect(Collectors.toList());
                    if (!teacherUIds.contains(uid)) {
                        continue;
                    }
                    calculateApproveHours(Collections.singletonList(kksz), map, key);
                }
            }
        }
        json.put("teacherList", map);
    }

    /**
     * @param json                结果数据
     * @param courseManageApprove 开课审批信息详情
     * @param formRightBtnVO      右侧按钮参数对象
     * @description 获取班级开课总学时
     * <AUTHOR>
     * @date 2024/7/26 15:34
     * @retrun void
     **/
    private void getCourseManageApproveClazzData(JSONObject json, CourseManageApprove courseManageApprove, FormRightBtnVO formRightBtnVO) {
        AprvSearchParam approveSearchParam = AprvSearchParam.builder().deptId(formRightBtnVO.getFid()).formAlias(CourseManageApprove.ALIAS).build();
        SearchStrBody searchStrBody = SearchStrBody.and();
        searchStrBody.createAndAdd(CourseManageApprove::getKkszsp_kkxnxq).eq(courseManageApprove.getKkszsp_kkxnxq());
        searchStrBody.createAndAdd(CourseManageApprove::getKkszsp_jxbzc).match(courseManageApprove.getKkszsp_jxbzc().toArray(new String[0]));
        List<CourseManageApprove> clazzList = ApproveTemplate.search(approveSearchParam, searchStrBody, CourseManageApprove.class);
        if (MyUtils.isEmpty(clazzList)) {
            return;
        }
        List<String> teachClazzArray = courseManageApprove.getKkszsp_jxbzc();
        String[] teachClazzCodeArray = courseManageApprove.getKkszsp_jxbzcbh().split("[,，]");
        Map<String, Integer> map = new HashMap<>();
        for (CourseManageApprove approve : clazzList) {
            if (MyUtils.isEmpty(approve.getKkszsp_kksz())) {
                continue;
            }
            for (int i = 0; i < teachClazzArray.size(); i++) {
                String teachClazz = teachClazzArray.get(i);
                String key = teachClazzCodeArray[i] + "|" + teachClazz;
                if (!approve.getKkszsp_jxbzc().contains(teachClazz)) {
                    continue;
                }
                calculateApproveHours(approve.getKkszsp_kksz(), map, key);
            }
        }
        json.put("clazzList", map);
    }

    /**
     * @param list 开课设置审批-开课设置信息
     * @param map  开课学时统计map
     * @param key  学时key
     * @description 计算开课学时
     * <AUTHOR>
     * @date 2024/7/26 15:25
     * @retrun void
     **/
    private void calculateApproveHours(List<Kkszsp_kksz> list, Map<String, Integer> map, String key) {
        for (Kkszsp_kksz kksz : list) {
            if (StringUtils.isBlank(kksz.getKkszsp_zxs()) || StringUtils.isBlank(kksz.getKkszsp_zc())) {
                continue;
            }
            int teachingWeeks = Arrays.stream(kksz.getKkszsp_zc().split("[,，]")).mapToInt(range -> {
                String[] rangeParts = range.split("-");
                return Integer.parseInt(rangeParts[rangeParts.length - 1]) - Integer.parseInt(rangeParts[0]) + 1;
            }).sum();
            int totalHours = teachingWeeks * Integer.parseInt(kksz.getKkszsp_zxs());
            map.merge(key, totalHours, Integer::sum);
        }
    }

    // ==================== 轮次同步教师相关辅助方法 ====================

    /**
     * 查找当前轮次
     *
     * @param roundList 轮次列表
     * @param round     当前轮次名称
     * @return 当前轮次信息
     */
    private Xnxqlczbd findCurrentRound(List<Xnxqlczbd> roundList, String round) {
        if (MyUtils.isEmpty(roundList) || StringUtils.isBlank(round)) {
            return null;
        }

        return roundList.stream()
                .filter(r -> Objects.equals(r.getXnxq_lc(), round))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找上一轮次
     *
     * @param roundList 轮次列表
     * @param round     当前轮次名称
     * @return 上一轮次，如果没有则返回null
     */
    private Xnxqlczbd findPreviousRound(List<Xnxqlczbd> roundList, String round) {
        try {
            String currentRoundNumber = ConversionNumberUtil.convertNumber(round.replaceAll("[^一二三四五六七八九十]", ""));
            if (StringUtils.isBlank(currentRoundNumber)) {
                return null;
            }

            int currentNum = Integer.parseInt(currentRoundNumber);
            if (currentNum <= 1) {
                return null;
            }
            String previousRoundName = round.replaceAll("\\d+", String.valueOf(currentNum - 1));
            if (round.contains("第") && round.contains("轮")) {
                String chineseNumber = NumberUtils.convert(currentNum - 1);
                previousRoundName = "第" + chineseNumber + "轮";
            }
            return roundList.stream()
                    .filter(r -> Objects.equals(r.getXnxq_lc(), previousRoundName))
                    .findFirst()
                    .orElse(null);

        } catch (Exception e) {
            log.warn("查找上一轮次失败，round: {}", round, e);
            return null;
        }
    }

    /**
     * 根据周次获取课程信息
     *
     * @param fid      机构ID
     * @param semester 学期
     * @param weeks    周次
     * @return 课程信息列表
     */
    private List<CourseInformationForm> getCoursesByWeeks(int fid, String semester, String weeks) {
        if (StringUtils.isBlank(weeks)) {
            return Collections.emptyList();
        }

        return FormUtils.listAll(
                SearchStrBodyType.AND,
                fid,
                CourseInformationForm.ALIAS,
                searchStrBody -> {
                    searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kkxq).eq(semester);
                    searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_zc).eq(weeks);
                }, CourseInformationForm.class);
    }

    /**
     * 同步教师信息
     *
     * @param currentCourses  当前轮次课程列表
     * @param previousCourses 上一轮次课程列表
     * @param currentRound    当前轮次信息
     * @param previousRound   上一轮次信息
     * @return 同步的课程数量
     */
    private int syncTeacherInfo(List<CourseInformationForm> currentCourses,
                               List<CourseInformationForm> previousCourses,
                               Xnxqlczbd currentRound,
                               Xnxqlczbd previousRound) {
        if (MyUtils.isEmpty(currentCourses) || MyUtils.isEmpty(previousCourses)) {
            log.warn("当前轮次或上一轮次课程为空，无法同步");
            return 0;
        }

        // 构建上一轮次课程的映射，以课程编号+班级为key
        Map<String, CourseInformationForm> previousCourseMap = previousCourses.stream()
                .filter(course -> StringUtils.isNotBlank(course.getKkxxb_kcbh()) &&
                                StringUtils.isNotBlank(course.getKkxxb_jxbmc()))
                .collect(Collectors.toMap(
                        course -> buildCourseKey(course),
                        course -> course,
                        (existing, replacement) -> existing // 如果有重复key，保留第一个
                ));

        int syncCount = 0;
        List<CourseInformationForm> toUpdate = new ArrayList<>();

        for (CourseInformationForm currentCourse : currentCourses) {
            String courseKey = buildCourseKey(currentCourse);
            CourseInformationForm previousCourse = previousCourseMap.get(courseKey);

            if (previousCourse != null && hasTeacherInfo(previousCourse)) {
                // 复制教师信息
                copyTeacherInfo(previousCourse, currentCourse);
                toUpdate.add(currentCourse);
                syncCount++;
            }
        }

        // 批量更新课程信息
        if (!toUpdate.isEmpty()) {
            batchUpdateCourses(toUpdate);
            log.info("批量更新课程教师信息，数量: {}", toUpdate.size());
        }

        return syncCount;
    }

    /**
     * 构建课程唯一标识key
     *
     * @param course 课程信息
     * @return 课程key
     */
    private String buildCourseKey(CourseInformationForm course) {
        return StringUtils.defaultString(course.getKkxxb_kcbh()) + "|" +
               StringUtils.defaultString(course.getKkxxb_jxbmc());
    }

    /**
     * 检查课程是否有教师信息
     *
     * @param course 课程信息
     * @return 是否有教师信息
     */
    private boolean hasTeacherInfo(CourseInformationForm course) {
        return MyUtils.isNotEmpty(course.getKkxxb_skjsxm()) ||
               StringUtils.isNotBlank(course.getKkxxb_skjs()) ||
               StringUtils.isNotBlank(course.getKkxxb_skjsgh()) ||
               MyUtils.isNotEmpty(course.getKkxxb_zjxm()) ||
               StringUtils.isNotBlank(course.getKkxxb_zjjsgh());
    }

    /**
     * 复制教师信息
     *
     * @param source 源课程（上一轮次）
     * @param target 目标课程（当前轮次）
     */
    private void copyTeacherInfo(CourseInformationForm source, CourseInformationForm target) {
        try {
            // 复制授课教师信息
            if (MyUtils.isNotEmpty(source.getKkxxb_skjsxm())) {
                target.setKkxxb_skjsxm(new ArrayList<>(source.getKkxxb_skjsxm()));
            }

            if (StringUtils.isNotBlank(source.getKkxxb_skjs())) {
                target.setKkxxb_skjs(source.getKkxxb_skjs());
            }

            if (StringUtils.isNotBlank(source.getKkxxb_skjsgh())) {
                target.setKkxxb_skjsgh(source.getKkxxb_skjsgh());
            }

            // 复制助教信息
            if (MyUtils.isNotEmpty(source.getKkxxb_zjxm())) {
                target.setKkxxb_zjxm(new ArrayList<>(source.getKkxxb_zjxm()));
            }

            if (StringUtils.isNotBlank(source.getKkxxb_zjjsgh())) {
                target.setKkxxb_zjjsgh(source.getKkxxb_zjjsgh());
            }

            log.debug("复制教师信息成功 - 课程: {}, 班级: {}, 授课教师: {}",
                target.getKkxxb_kcmc(), target.getKkxxb_jxbmc(),
                MyUtils.isNotEmpty(target.getKkxxb_skjsxm()) ?
                    target.getKkxxb_skjsxm().stream().map(IdName::getUname).collect(Collectors.joining(",")) : "无");

        } catch (Exception e) {
            log.error("复制教师信息失败 - 课程: {}, 班级: {}",
                target.getKkxxb_kcmc(), target.getKkxxb_jxbmc(), e);
        }
    }

    /**
     * 批量更新课程信息
     *
     * @param courses 要更新的课程列表
     */
    private void batchUpdateCourses(List<CourseInformationForm> courses) {
        if (MyUtils.isEmpty(courses)) {
            return;
        }

        try {
            // 分批处理，避免一次性更新过多数据
            int batchSize = 50;
            for (int i = 0; i < courses.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, courses.size());
                List<CourseInformationForm> batch = courses.subList(i, endIndex);

                for (CourseInformationForm course : batch) {
                    FormUtils.update(course);
                }

                log.debug("批量更新课程信息 - 批次: {}/{}, 数量: {}",
                    (i / batchSize) + 1, (courses.size() + batchSize - 1) / batchSize, batch.size());
            }

        } catch (Exception e) {
            log.error("批量更新课程信息失败", e);
            throw new RuntimeException("更新课程信息失败: " + e.getMessage());
        }
    }
}
