<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>培养方案</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialog.css'}">
    <style>
        .dialog .dialog-con {
            height: 220px;
        }

        .j-search-con .j-select-year ul {
            max-height: 130px;
        }
    </style>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>
<body>
<div class="dialog" id="invigilateMax" style="width: 438px;">
    <div class="dialog-con">
        <div class="item">
            <div class="label">选择轮次</div>
            <div class="j-search-con single-box">
                <input type="text" name="grade" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <ul>
                        <li>第二轮</li>
                        <li>第四轮</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="invigilateSure" style="width: 88px;">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    layui.use(['form', 'layer'], function () {
        const layer = layui.layer;
        //监听提交
        $(".pu-sure").click(function () {
            let loading = layer.load(1);
            $.post("/api/form/cultivation/topBtn/exportCultivationProcess", {
                queryId: queryId,
                formId: formId,
                fid: fid,
                uid: uid,
                templateId: $('ul[name="tpl"] li.active').attr('data-id')
            }, function (result) {
                if (result.success) {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                } else {
                    layer.msg(result.message, {icon: 2, time: 3000});
                }
                layer.close(loading);
            }, "json");
        })
        //  选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {
                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }

        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow');
            const sibling = $(this).parents('.item').siblings();
            sibling.find('.j-arrow').removeClass('j-arrow-slide');
            sibling.find('.j-select-year').removeClass('slideShow');
            stopBubble(e)
        })

        $(document).on("click", function (event) {
            const _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })
    })
    $(".pu-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

</script>
</html>