package com.chaoxing.academic.service.cultivation;

import com.alibaba.fastjson.JSONObject;
import com.chaoxing.academic.design.template.bo.FormTopBtnBO;
import com.chaoxing.academic.entity.form.cultivation.CourseAllocationResultForm;
import com.chaoxing.academic.entity.form.cultivation.OpenCourseRecordForm;
import com.chaoxing.academic.entity.vo.R;
import com.chaoxing.academic.entity.vo.form.FormRightBtnVO;

import java.util.List;

public interface CultivationPopService {

    List<OpenCourseRecordForm> getFormData(long fid, int week, String term);

    R<JSONObject> exportDataToExcel(Integer weekly, Integer type, String term);

    R<JSONObject> exportCourseDataToExcel(Integer fid, String semester, Integer dataType);

    void appointTeacher(FormTopBtnBO formTopBtnBO, String formUserId, String formField, String formFieldVal, Integer syncScoreTeacher);

    R termCreditSummary(FormRightBtnVO formRightBtnVO);

    R getSubjectGroupData(Integer fid);

    /**
     * @param semester  学年学期
     * @param teacherNo 教师工号
     * @param fid       单位id
     * @description 获取教师周课时
     * <AUTHOR>
     * @date 2024/6/4 15:54
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R getTeacherWeekHour(String semester, String teacherNo, Integer fid);

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @description 获取开课总学时统计数据
     * <AUTHOR>
     * @date 2024/7/25 9:40
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R totalHoursStatisticsData(FormRightBtnVO formRightBtnVO);

    /**
     * @param fid 单位id
     * @param fid 单位id
     * @description 下载开课信息导入模板
     * <AUTHOR>
     * @date 2024/9/4 20:22
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R downImportTpl(Integer fid, String fileName);

    /**
     * @param formRightBtnVO 右侧按钮参数对象
     * @param operateType    1 修改教学计划 2 停用教学计划
     * @description 教学计划变更操作校验
     * <AUTHOR>
     * @date 2024/10/23 15:24
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R teachPlanChangeOperateVerify(FormRightBtnVO formRightBtnVO, Integer operateType);

    /**
     * @param major 专业
     * @param grade 年级
     * @param fid   单位id
     * @description 根据专业和年级获取班级列表
     * <AUTHOR>
     * @date 2024/11/13 16:20
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R getClassByMajorAndGrade(String major, String grade, Integer fid);

    /**
     * @param fid    单位id
     * @param formId 表单id
     * @description 获取培养方案打印模板
     * <AUTHOR>
     * @date 2024/12/5 16:30
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    JSONObject getTrainingProgramTpl(Integer fid, Integer formId);

    /**
     * @param info  筛选条件
     * @param page  当前页
     * @param limit 每页显示条数
     * @description 获取配课数据
     * <AUTHOR>
     * @date 2024/12/5 16:30
     * @retrun com.alibaba.fastjson.JSONObject
     **/
    R getCourseAllocationData(CourseAllocationResultForm info, int page, int limit);
    
    /**
     * @param formUserId 专业教学计划数据id
     * @description 删除
     * <AUTHOR>
     * @date 2025/6/24 19:28
     * @retrun java.lang.Boolean
     **/
    Boolean deleteMajorTeachPlanValidate(String formUserId);

    /**
     * @param formUserId 专业教学计划数据id
     * @description 删除专业教学计划
     * <AUTHOR>
     * @date 2025/6/24 20:15
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R deleteMajorTeachPlan(String formUserId);

    /**
     * @param round 轮次
     * @description 天津机电根据轮次同步授课教师
     * <AUTHOR>
     * @date 2025/8/5 8:28
     * @retrun com.chaoxing.academic.entity.vo.R
     **/
    R roundSyncTeacher(String round);

}
