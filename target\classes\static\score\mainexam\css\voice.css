.borDer {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.35);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes walk {
  from {
    background-position: 0px;
  }
  to {
    background-position: -168px;
  }
}
@keyframes rotates {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.hide {
  display: none;
}
#voice .identify {
  display: none;
}
#voice .identify .identifying {
  width: 191px;
  height: 191px;
  margin: 0 auto;
  position: relative;
  margin-top: 17px;
}
#voice .identify .identifying h3 {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  color: #1A79FF;
  text-align: center;
  font-size: 25px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
#voice .identify .identifying img {
  display: block;
  animation: rotates 1.5s linear infinite;
}
#voice .popup-con {
  width: 440px;
  height: 318px;
  background-color: rgba(49, 62, 89, 0.95);
  border-radius: 10px;
  position: relative;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0;
  padding-top: 41px;
}
#voice .popup-con .incomplete {
  position: absolute;
  z-index: 999;
  width: 308px;
  height: 154px;
  background-color: #fff;
  border-radius: 7px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}
#voice .popup-con .incomplete .inc-con {
  height: 104px;
  display: flex;
  justify-content: center;
  align-items: center;
}
#voice .popup-con .incomplete .inc-con p {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.8);
  padding-left: 19px;
  background: url(../images/voice-warn.png) no-repeat left center;
  height: 20px;
  line-height: 20px;
}
#voice .popup-con .incomplete .inc-footer {
  border-top: 1px solid #E5E6EB;
  height: 50px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: flex-end;
}
#voice .popup-con .incomplete .inc-footer .cancle {
  border-radius: 12.6px;
  border: 1px solid #474C59;
  display: block;
  width: 62px;
  height: 24px;
  text-align: center;
  line-height: 26px;
  cursor: pointer;
  font-size: 12px;
  color: #474C59;
  margin-right: 14px;
}
#voice .popup-con .incomplete .inc-footer .confirm {
  border-radius: 12.6px;
  background: #F36161;
  display: block;
  width: 62px;
  height: 26px;
  text-align: center;
  line-height: 26px;
  cursor: pointer;
  font-size: 12px;
  color: #ffffff;
}
#voice .popup-con .view-boxs {
  position: absolute;
  z-index: 999;
  width: 308px;
  height: 200px;
  background-color: #fff;
  border-radius: 7px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
}
#voice .popup-con .view-boxs .view-head {
  width: 100%;
  height: 40px;
  border-bottom: 1px solid #E5E6EB;
  line-height: 39px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0 21px;
  color: #1d2129;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
#voice .popup-con .view-boxs .view-con {
  width: 100%;
  height: 110px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 21px;
  padding-bottom: 0;
}
#voice .popup-con .view-boxs .view-con p {
  color: #A8ACB3;
  line-height: 18px;
  font-size: 12px;
  font-weight: 400;
}
#voice .popup-con .view-boxs .view-con p em {
  font-weight: 600;
}
#voice .popup-con .view-boxs .view-footer {
  border-top: 1px solid #E5E6EB;
  height: 50px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: flex-end;
}
#voice .popup-con .view-boxs .view-footer span {
  border-radius: 12.6px;
  background: #4D88FF;
  box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
  display: block;
  width: 62px;
  height: 26px;
  text-align: center;
  line-height: 26px;
  cursor: pointer;
  font-size: 12px;
  color: #ffffff;
}
#voice .popup-con .closeVoice {
  cursor: pointer;
  position: absolute;
  top: 16px;
  right: 13px;
  width: 18px;
  height: 18px;
  background: url(../images/voice-close.png) repeat-x center;
}
#voice .popup-con .p-top {
  width: 100%;
  height: 35px;
  line-height: 35px;
  font-size: 25px;
  font-weight: 400;
  color: #1A79FF;
  text-align: center;
  margin-bottom: 35px;
}
#voice .popup-con .p-con {
  margin-bottom: 30px;
  position: relative;
}
#voice .popup-con .p-con .loading {
  position: absolute;
  left: 5px;
  top: 0;
  width: 168px;
  height: 70px;
  background: url(../images/loading3.png) no-repeat left center;
  animation: walk 2.5s steps(24) infinite;
}
#voice .popup-con .p-con .loading1 {
  position: absolute;
  right: 5px;
  top: 0;
  width: 168px;
  height: 70px;
  background: url(../images/loading3.png) no-repeat left center;
  animation: walk 2.5s steps(24) infinite;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
#voice .popup-con .p-con .img {
  width: 68px;
  height: 68px;
  margin: 0 auto 14px;
  position: relative;
  cursor: pointer;
}
#voice .popup-con .p-con .img img {
  display: block;
  position: relative;
  z-index: 991;
}
#voice .popup-con .p-con .img .glow {
  z-index: 990;
  position: absolute;
  width: 68px;
  height: 68px;
  left: 50%;
  top: 50%;
  margin-left: -34px;
  margin-top: -34px;
  border-radius: 200px;
  background: rgba(26, 121, 255, 0.3);
  animation: pulse 1.5s ease-in-out infinite;
}
#voice .popup-con .p-con .info {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: center;
  height: 24px;
}
#voice .popup-con .p-con .info .time {
  color: #eeeef0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#voice .popup-con .p-con .info .time .layui-input {
  display: none;
  height: 24px;
  width: 40px;
  margin-right: 8px;
}
#voice .popup-con .p-con .info .edit {
  color: #1c71e9;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin: 0 9px;
  cursor: pointer;
}
#voice .popup-con .p-con .info .seek {
  width: 12px;
  height: 12px;
  background: url(../images/voice-info.png) no-repeat center;
  cursor: pointer;
  position: relative;
}
#voice .popup-con .p-con .info .seek:hover span {
  display: block;
}
#voice .popup-con .p-con .info .seek span {
  display: none;
  position: absolute;
  top: -40px;
  left: -20px;
  margin-left: -190px;
  width: 380px;
  height: 38px;
  background: url(../images/toolTips.png) no-repeat center;
}
#voice .popup-con .view-instructions {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: center;
  height: 20px;
}
#voice .popup-con .view-instructions p {
  color: #a8acb3;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
#voice .popup-con .view-instructions span {
  color: #3a8bff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  cursor: pointer;
  margin: 0 7px;
}
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}
input[type="number"] {
  -moz-appearance: textfield;
}
