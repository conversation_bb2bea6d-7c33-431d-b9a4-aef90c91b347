/*
录音
https://github.com/xiangyuecn/Recorder
src: engine/g711x.js
*/
!function(){"use strict";var e=function(u,e,i,o){Recorder.prototype["enc_"+u]={stable:!0,fast:!0,testmsg:e+"；"+u+"音频文件无法直接播放，可用Recorder."+u+"2wav()转码成wav播放；采样率比特率设置无效，固定为8000hz采样率、16位，每个采样压缩成8位存储，音频文件大小为8000字节/秒"},Recorder.prototype[u]=function(e,r,t){var a=this.set,n=a.sampleRate,o=8e3;if(a.bitRate=16,(a.sampleRate=o)<n)e=Recorder.SampleData([e],n,o).data;else if(n<o)return void t("数据采样率低于"+o);var c=i(e);r(new Blob([c.buffer],{type:"audio/"+u}))},Recorder[u+"_decode"]=function(e){return o(e)},Recorder[u+"2wav"]=function(e,t,a){if(Recorder.prototype.wav){var n=new FileReader;n.onloadend=function(){var e=new Uint8Array(n.result),r=o(e);Recorder({type:"wav",sampleRate:8e3,bitRate:16}).mock(r,8e3).stop(function(e,r){t(e,r)},a)},n.readAsArrayBuffer(e)}else a(u+"2wav必须先加载wav编码器wav.js")}},u=[1,2,3,3,4,4,4,4,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];e("g711a","G.711 A-law (pcma)",function(e){for(var r=new Uint8Array(e.length),t=0;t<e.length;t++){var a,n=e[t];0<=n?a=213:(a=85,n=-n-1);var o=(u[n>>8&127]||8)-1,c=o<<4;c|=o<2?n>>4&15:n>>o+3&15,r[t]=c^a}return r},function(e){for(var r=new Int16Array(e.length),t=0;t<e.length;t++){var a=85^e[t],n=(15&a)<<4,o=(112&a)>>4;switch(o){case 0:n+=8;break;case 1:n+=264;break;default:n+=264,n<<=o-1}r[t]=128&a?n:-n}return r}),e("g711u","G.711 μ-law (pcmu、mu-law)",function(e){for(var r=new Uint8Array(e.length),t=0;t<e.length;t++){var a,n=e[t];n<0?(n=132-n,a=127):(n+=132,a=255);var o=(u[n>>8&127]||8)-1,c=o<<4|n>>o+3&15;r[t]=c^a}return r},function(e){for(var r=new Int16Array(e.length),t=0;t<e.length;t++){var a=~e[t],n=132+((15&a)<<3);n<<=(112&a)>>4,r[t]=128&a?132-n:n-132}return r})}();