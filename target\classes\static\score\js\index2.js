var timer;

$(document).ready(function () {
    $(".jc").click();
    $(".m-tab").on("click", "ul li", function () {
        $(".m-tab").find(".cur").removeClass("cur");
        $(this).addClass("cur");
    });
});

$(".jc").click(function () {
    $(".con-right").hide();
    let url = $(".frame_jc").attr('url');
    initIframe(adaptScheme(url), 'jc');
});
$(".fx").click(function () {
    $(".con-right").hide();
    let url = $(".frame_fx").attr('url');
    initIframe(adaptScheme(url), 'fx');
});
$(".jz").click(function () {
    $(".con-right").hide();
    let url = $(".frame_jz").attr('url');
    initIframe(adaptScheme(url), 'jz');
});
$(".js").click(function () {
    $(".con-right").hide();
    let url = $(".frame_js").attr('url');
    initIframe(adaptScheme(url), 'js');
});
$(".xf").click(function () {
    $(".con-right").hide();
    let url = $(".frame_xf").attr('url');
    initIframe(adaptScheme(url), 'xf');
});
$(".jd").click(function () {
    $(".con-right").hide();
    let url = $(".frame_jd").attr('url');
    initIframe(adaptScheme(url), 'jd');
});
$(".qt").click(function () {
    $(".con-right").hide();
    let url = $(".frame_qt").attr('url');
    initIframe(adaptScheme(url), 'qt');
});

/**
 * 修正协议
 * @param url
 * @returns {string}
 */
function adaptScheme(url) {
    url = url.toString();
    if (url.startsWith("http:")) {
        return url.toString().replace("http:", window.location.protocol);
    } else if (url.startsWith("https:")) {
        return url.toString().replace("https:", window.location.protocol);
    }

    return url;
}

function initIframe(src, model) {

    clearTimeout(timer);

    timer = setTimeout(function () {
        let oldFrame = $("#frame_content-" + model);
        if (oldFrame.length > 0) {
            oldFrame.remove();
        }
        ContentLoader.createContentFrame('frame_content', src, "1", model);
    }, 30);
}

function iframeAutoResize(doc) {
    var ifm = document.getElementById("frame_content");
    var subWeb = "";
    try {
        subWeb = document.frames ? document.frames["frame_content"].document : ifm.contentDocument;
    } catch (err) {
        return false;
    }
    if (ifm != null && subWeb != null) {
        $("#frame_content").height(subWeb.body.clientHeight + 100 + "px");
    }
}


function secIframeAutoResize() {
    iframeAutoResize(document.getElementById("frame_content"));
}