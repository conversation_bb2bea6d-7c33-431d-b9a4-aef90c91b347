.borDer {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 14px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
body {
  font-family: 'PingFang SC';
  background: #F7F8FA;
}
.w240 {
  width: 240px;
}
.w124 {
  width: 124px;
}
.w110 {
  width: 110px;
}
.w60 {
  width: 60px;
}
.disabled .layui-table-cell .opbtn {
  color: #999 !important;
}
.editable .layui-table-cell {
  overflow: visible;
  height: auto;
  line-height: normal;
}
.editable .layui-table-cell .final {
  display: none;
}
.editable .layui-table-cell .inputs input {
  display: block;
}
.editable .layui-table-cell .inputs input {
  text-align: center;
  padding-left: 0;
}
.editable .layui-table-cell .selects .layui-form-select {
  display: block;
}
.layui-table-cell {
  overflow: visible;
  height: auto;
  line-height: normal;
}
.layui-table-cell .inputs input {
  display: none;
}
.layui-table-cell .inputs input {
  text-align: center;
  padding-left: 0;
}
.layui-table-cell .selects .layui-form-select {
  display: none;
}
.main {
  padding: 22px 20px 20px;
}
.main .m-tab {
  width: 100%;
  height: 60px;
  border-radius: 8px;
  background: #FFF;
  margin-bottom: 20px;
}
.main .m-tab ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
}
.main .m-tab ul li {
  width: 156px;
  font-size: 16px;
  height: 60px;
  line-height: 60px;
  color: #474C59;
  font-style: normal;
  font-weight: 400;
  text-align: center;
  cursor: pointer;
  position: relative;
}
.main .m-tab ul li.cur {
  color: #4D88FF;
  font-weight: 500;
}
.main .m-tab ul li.cur:after {
  width: 48px;
  height: 4px;
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  margin-left: -24px;
  border-radius: 2px;
  background: #3A8BFF;
}
.main .content {
  width: 100%;
  min-height: calc(100vh - 42px);
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 32px 20px;
}
.add-lab {
  width: 100%;
  height: 36px;
  margin-bottom: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.add-lab span {
  width: 92px;
  height: 36px;
  border-radius: 6px;
  background: #3A8BFF;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
}
.main .content .filter-box {
  margin-bottom: 24px;
}
.main .content .filter-box .layui-form {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.main .content .filter-box .layui-form .layui-form-item {
  margin-right: 15px;
}
.main .content .filter-box .layui-form .layui-form-label {
  width: auto;
  margin-right: 14px;
}
.main .content .filter-box .layui-form .layui-input-block .btns {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 34px;
}
.main .content .filter-box .layui-form .layui-input-block .btns .search {
  width: 60px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 6px;
  background: #1A79FF;
  cursor: pointer;
  margin-right: 20px;
}
.main .content .filter-box .layui-form .layui-input-block .btns .resetting {
  border-radius: 6px;
  border: 1px solid #8CBBFF;
  width: 60px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  font-size: 14px;
  color: #3a8bff;
  cursor: pointer;
}
.save-settings {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: center;
  margin-top: 40px;
}
.save-settings span {
  width: 104px;
  height: 36px;
  border-radius: 6px;
  background-color: #4D88FF;
  cursor: pointer;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  color: #FFFFFF;
}
.main .content .btn-lists {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
  margin-bottom: 37px;
}
.main .content .btn-lists .btn {
  padding: 0 14px;
  height: 36px;
  line-height: 36px;
  border-radius: 4px;
  background: #1A79FF;
  margin-right: 14px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}
.main .content .table .layui-table-cell .stute {
  border-radius: 4px;
  background: #DCFAED;
  width: 49px;
  height: 23px;
  text-align: center;
  line-height: 23px;
  font-size: 12px;
  color: #00b368;
}
#ruleTemplateDetails {
  width: 850px;
}
#ruleTemplateDetails .popup-con .formula {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 4px;
  border: 1px solid #1A79FF;
  background: #F7F8FA;
  width: 795px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  min-height: 49px;
  overflow: hidden;
  align-items: flex-start;
  position: relative;
  padding-left: 100px;
}
#ruleTemplateDetails .popup-con .formula .totals {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: center;
  justify-content: flex-end;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  color: #4e5969;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  width: 100px;
  text-align: right;
  padding-right: 10px;
  flex-shrink: 0;
  border-right: 1px solid #1A79FF;
  background: #F1F3F6;
  height: 100%;
  line-height: 100%;
}
#ruleTemplateDetails .popup-con .formula .inform {
  padding: 14px 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
}
#ruleTemplateDetails .popup-con .formula .inform span {
  color: #86909c;
  font-size: 12px;
  margin: 0 2px;
  margin-bottom: 3px;
  margin-top: 3px;
}
#ruleTemplateDetails .popup-con .formula .inform span.tit {
  border-radius: 10.5px;
  background: #4D88FF;
  padding: 0 7px;
  height: 20px;
  line-height: 20px;
  color: #fff;
}
.inputTeacher {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 30px;
}
.inputTeacher .name {
  height: 24px;
  font-size: 14px;
  color: #474c59;
  line-height: 24px;
  margin-right: 16px;
}
.inputTeacher .it-con {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.inputTeacher .it-con .person-list ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.inputTeacher .it-con .person-list ul li {
  border-radius: 2px;
  background: rgba(0, 132, 255, 0.12);
  padding-left: 6px;
  padding-right: 2px;
  height: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 12px;
}
.inputTeacher .it-con .person-list ul li span {
  font-size: 13px;
  color: #3a8bff;
}
.inputTeacher .it-con .person-list ul li em {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background: url(../images/close-small.png) no-repeat center;
}
.inputTeacher .it-con .person-list ul li.selects {
  border-radius: 2px;
  border: 1px solid #8CBBFF;
  width: 62px;
  height: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding-left: 25px;
  background: url(../images/plus-iconss.png) no-repeat 12px center;
  cursor: pointer;
}
.inputTeacher .it-con .person-list ul li.selects span {
  font-size: 12px;
  color: #3a8bff;
}
#addPoups,
#ruleAddPoups {
  width: 880px;
}
#addPoups .popup-con .inform,
#ruleAddPoups .popup-con .inform {
  margin-bottom: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#addPoups .popup-con .inform .lable,
#ruleAddPoups .popup-con .inform .lable {
  margin-right: 40px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  font-size: 14px;
  color: #2c56b8;
}
#addPoups .popup-con .inputTeacher,
#ruleAddPoups .popup-con .inputTeacher {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 30px;
}
#addPoups .popup-con .inputTeacher .name,
#ruleAddPoups .popup-con .inputTeacher .name {
  height: 24px;
  font-size: 14px;
  color: #474c59;
  line-height: 24px;
  margin-right: 16px;
}
#addPoups .popup-con .inputTeacher .it-con,
#ruleAddPoups .popup-con .inputTeacher .it-con {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#addPoups .popup-con .inputTeacher .it-con .person-list ul,
#ruleAddPoups .popup-con .inputTeacher .it-con .person-list ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#addPoups .popup-con .inputTeacher .it-con .person-list ul li,
#ruleAddPoups .popup-con .inputTeacher .it-con .person-list ul li {
  border-radius: 2px;
  background: rgba(0, 132, 255, 0.12);
  padding-left: 6px;
  padding-right: 2px;
  height: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 12px;
}
#addPoups .popup-con .inputTeacher .it-con .person-list ul li span,
#ruleAddPoups .popup-con .inputTeacher .it-con .person-list ul li span {
  font-size: 13px;
  color: #3a8bff;
}
#addPoups .popup-con .inputTeacher .it-con .person-list ul li em,
#ruleAddPoups .popup-con .inputTeacher .it-con .person-list ul li em {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background: url(../images/close-small.png) no-repeat center;
}
#addPoups .popup-con .inputTeacher .it-con .person-list ul li.selects,
#ruleAddPoups .popup-con .inputTeacher .it-con .person-list ul li.selects {
  border-radius: 2px;
  border: 1px solid #8CBBFF;
  width: 62px;
  height: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding-left: 25px;
  background: url(../images/plus-iconss.png) no-repeat 12px center;
  cursor: pointer;
}
#addPoups .popup-con .inputTeacher .it-con .person-list ul li.selects span,
#ruleAddPoups .popup-con .inputTeacher .it-con .person-list ul li.selects span {
  font-size: 12px;
  color: #3a8bff;
}
#addPoups .popup-con h2,
#ruleAddPoups .popup-con h2 {
  font-size: 14px;
  color: #000000;
  font-weight: bold;
  line-height: 20px;
  padding-left: 10px;
  position: relative;
  margin-bottom: 15px;
}
#addPoups .popup-con h2:after,
#ruleAddPoups .popup-con h2:after {
  content: "";
  position: absolute;
  left: 0;
  top: 2px;
  width: 4px;
  height: 16px;
  background: #1A79FF;
}
#addPoups .popup-con .addType .layui-form-item .layui-form-label,
#ruleAddPoups .popup-con .addType .layui-form-item .layui-form-label {
  font-size: 14px;
  color: #000000;
  font-weight: 500;
}
#addPoups .popup-con .score-list,
#ruleAddPoups .popup-con .score-list {
  padding-top: 11px;
}
#addPoups .popup-con .score-list .item,
#ruleAddPoups .popup-con .score-list .item {
  display: none;
}
#addPoups .popup-con .score-list .item.active,
#ruleAddPoups .popup-con .score-list .item.active {
  display: block;
}
#addPoups .popup-con .score-list .item .add-lab,
#ruleAddPoups .popup-con .score-list .item .add-lab {
  width: 100%;
  height: 36px;
  margin-bottom: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#addPoups .popup-con .score-list .item .add-lab span,
#ruleAddPoups .popup-con .score-list .item .add-lab span {
  width: 92px;
  height: 36px;
  border-radius: 6px;
  background: #1A79FF;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
}
#addPoups .popup-con .score-list .item .table,
#ruleAddPoups .popup-con .score-list .item .table {
  margin-bottom: 16px;
}
#addPoups .popup-con .layui-form.radio-item .layui-form-item,
#ruleAddPoups .popup-con .layui-form.radio-item .layui-form-item {
  margin-bottom: 18px;
}
#addPoups .popup-con .layui-form.radio-item .layui-form-item .layui-form-label,
#ruleAddPoups .popup-con .layui-form.radio-item .layui-form-item .layui-form-label {
  font-size: 14px;
  color: #8a8b99;
  font-weight: 500;
  margin-right: 32px;
  width: auto;
}
#addPoups .popup-con .layui-form.radio-item .layui-form-item .layui-form-radio,
#ruleAddPoups .popup-con .layui-form.radio-item .layui-form-item .layui-form-radio {
  font-size: 14px;
  color: #474c59;
}
#addPoups .popup-con .layui-form.radio-item .layui-form-item .templat-details,
#ruleAddPoups .popup-con .layui-form.radio-item .layui-form-item .templat-details {
  color: #3a8bff;
  font-size: 14px;
  cursor: pointer;
}
#addPoups .popup-con .layui-form.radio-item .layui-input-block,
#ruleAddPoups .popup-con .layui-form.radio-item .layui-input-block {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#addPoups .popup-con .layui-form.flex-item,
#ruleAddPoups .popup-con .layui-form.flex-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#addPoups .popup-con .layui-form.flex-item .layui-form-item,
#ruleAddPoups .popup-con .layui-form.flex-item .layui-form-item {
  margin-right: 68px;
}
#addPoups .popup-con .layui-form.flex-item .layui-form-item:last-child,
#ruleAddPoups .popup-con .layui-form.flex-item .layui-form-item:last-child {
  margin-right: 0;
}
#addPoups .popup-con .layui-form .layui-form-item .layui-form-label,
#ruleAddPoups .popup-con .layui-form .layui-form-item .layui-form-label {
  width: auto;
}
.layui-form .layui-form-item {
  margin-bottom: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.layui-form .layui-form-item .limit-switch {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.layui-form .layui-form-item .limit-switch span {
  line-height: 34px;
  color: #c9cdd4;
  margin-left: 10px;
}
.layui-form .layui-form-item .limit-switch .layui-form-switch {
  border-radius: 3px;
  background: #D2D3D8;
  height: 14px;
  line-height: 14px;
  min-width: 28px;
  padding: 0 0;
  margin-top: 10px;
  border: none;
}
.layui-form .layui-form-item .limit-switch .layui-form-switch i {
  left: 2px;
  top: 2px;
  width: 12px;
  height: 10px;
  border-radius: 1px;
  background: #FFF;
  margin-left: 0;
}
.layui-form .layui-form-item .limit-switch .layui-form-onswitch {
  border-radius: 3px;
  background: #537AF6;
}
.layui-form .layui-form-item .limit-switch .layui-form-onswitch i {
  left: 100%;
  margin-left: -14px;
  background-color: #fff;
}
.layui-form .layui-form-item .limit-switch .tit {
  margin-left: 10px;
  padding-top: 9px;
}
.layui-form .layui-form-item .limit-switch .tit h4 {
  color: #131B26;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 4px;
}
.layui-form .layui-form-item .limit-switch .tit p {
  color: #8A8B99;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
}
.layui-form .layui-form-item .layui-form-label {
  color: #1D2129;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-right: 16px;
  height: 34px;
  line-height: 34px;
  width: 84px;
}
.layui-form .layui-form-item .layui-form-label em {
  font-size: 16px;
  color: #E23131;
  display: inline-block;
  margin-right: 5px;
}
.layui-form .layui-form-item .times {
  margin-right: 16px;
  width: 240px;
  height: 34px;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: pointer;
  background: url(../images/times.png) no-repeat 220px center;
  background-size: 12px;
}
.layui-form .layui-form-item .times .layui-input {
  background-color: transparent;
  color: #4E5969;
  font-size: 14px;
  cursor: pointer;
}
.layui-form .layui-form-item .layui-input-block {
  margin-left: 0;
}
.layui-form .layui-form-item .layui-input-block.w240 {
  width: 240px;
}
.layui-form .layui-form-item .layui-input-block.w60 {
  width: 60px;
}
.layui-form .layui-form-item .layui-input-block.w275 {
  width: 275px;
}
.layui-form .layui-form-item .layui-input-block .lab {
  height: 34px;
  margin-bottom: 6px;
  position: relative;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.layui-form .layui-form-item .layui-input-block .lab:last-child {
  margin-bottom: 0;
}
.layui-form .layui-form-item .layui-input-block .lab .layui-form-radio {
  margin: 0;
}
.layui-form .layui-form-item .layui-input-block .lab .introIcon:hover .bubble {
  display: block;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble {
  display: none;
  position: absolute;
  left: 100%;
  top: 50%;
  margin-top: -15px;
  padding-left: 17px;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble:after {
  content: '';
  position: absolute;
  left: 13px;
  top: 50%;
  width: 4px;
  height: 10px;
  margin-top: -5px;
  background: url(../images/triangle.png) no-repeat left center;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble span {
  display: block;
  border-radius: 4px;
  background: #F2F5F7;
  height: 29px;
  padding: 0 16px;
  font-size: 12px;
  line-height: 29px;
  color: #737B86;
  white-space: nowrap;
}
.layui-form .layui-form-item .layui-input-block .lab .bubble span em {
  color: #1A79FF;
  cursor: pointer;
  margin-left: 9px;
}
.hide {
  display: none !important;
}
.scrollBox::-webkit-scrollbar {
  width: 8px;
  height: 10px;
}
.scrollBox::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: #DADFE6;
}
.scrollBox::-webkit-scrollbar-track {
  border-radius: 6px;
}
.scrollBox1::-webkit-scrollbar {
  width: 4px;
  height: 10px;
}
.scrollBox1::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #DADFE6;
}
.scrollBox1::-webkit-scrollbar-track {
  border-radius: 10px;
}
#template-details {
  width: 850px;
}
.content .top {
  width: 100%;
  height: 50px;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
}
.content .top .title {
  padding-left: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
}
.content .top .title .back {
  padding-left: 22px;
  background: url(../images/back-icon.png) no-repeat left center;
  background-size: 16px;
  color: #7D92B3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}
.content .top .title .levelone {
  padding-left: 9px;
  position: relative;
  color: #1d2129;
  font-size: 16px;
  margin-right: 6px;
  font-weight: 400;
}
.content .top .title .levelone:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
}
.content .top .title .icon {
  width: 12px;
  height: 12px;
  background: url(../images/right-arrow.png) no-repeat center;
  background-size: 12px;
  margin-right: 6px;
}
.content .top .title .leveltwo {
  color: #1D2129;
  font-weight: 400;
  font-size: 16px;
}
.content .top .title .inform {
  font-size: 14px;
  color: #86909c;
  font-style: normal;
  margin-left: 16px;
  font-weight: 500;
}
.content .top .btn {
  position: absolute;
  top: 17px;
  right: 28px;
  width: 116px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
  background: #4D88FF;
  box-shadow: 0px 0px 10px #4D88FF;
  border-radius: 4px;
}
.content .top h4 {
  position: relative;
  color: #1D2129;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
}
.content .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.content .tab {
  height: 58px;
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0 20px;
  margin-bottom: 17px;
}
.content .tab ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.content .tab ul li {
  padding: 0 15px;
  margin-right: 24px;
  height: 58px;
  line-height: 58px;
  font-size: 16px;
  color: #474c59;
  cursor: pointer;
}
.content .tab ul li.cur {
  color: #3a8bff;
  font-weight: 500;
  position: relative;
}
.content .tab ul li.cur:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  border-radius: 1.5px;
  background: #3B90FF;
  width: 32px;
  margin-left: -16px;
  height: 4px;
}
.content .btns-list {
  width: 100%;
  height: 36px;
  margin-bottom: 45px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.content .btns-list .enter {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.content .btns-list .enter em {
  border-radius: 6px;
  border: 1px solid #8CBBFF;
  width: 68px;
  height: 36px;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  font-size: 14px;
  color: #3a8bff;
  margin-right: 14px;
  display: none;
}
.content .btns-list span {
  width: 92px;
  height: 36px;
  border-radius: 6px;
  background: #1A79FF;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
  margin-right: 14px;
}
.content .item-list .item {
  display: none;
}
.content .item-list .item.active {
  display: block;
}
.content .item-list .item .table {
  padding: 0 30px;
}
#listSorting {
  width: 590px;
}
.layui-upload-drag {
  padding: 0;
  border: none;
}
.layui-elem-quote {
  background-color: transparent;
  border-color: transparent;
}
#formulaConversion {
  width: 860px;
}
#formulaConversion .bottom {
  position: relative;
}
#formulaConversion .bottom .download {
  position: absolute;
  left: 30px;
  top: 17px;
  width: 140px;
  height: 34px;
  line-height: 34px;
  border-radius: 4px;
  background: #4D88FF;
  padding-left: 40px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  background: #4D88FF url(../images/xz-icons.png) no-repeat 16px center;
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
}
.formulaConversion .label-box {
  width: 800px;
  height: 184px;
  border: 1px dashed #D4D6D9;
  border-radius: 2px;
  border: 1px dashed #E5E6EB;
  background: #F2F3F5;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.formulaConversion .label-box .plus {
  margin: 50px auto 24px;
  width: 14px;
  height: 14px;
  background: url(../images/plus.png) no-repeat center;
  display: block;
}
.formulaConversion .label-box p {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  color: #1d2129;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  margin: 0 auto 4px;
}
.formulaConversion .label-box p.tips {
  font-size: 12px;
  color: #86909c;
  line-height: 20px;
}
.formulaConversion .label-box .import {
  width: 120px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  cursor: pointer;
  background: #1A79FF;
  margin: 0 auto;
  border-radius: 50px;
}
.formulaConversion .label-box .import span {
  display: inline-block;
  padding-left: 22px;
  background: url(../images/down-icons.png) no-repeat left center;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}
#setInputTeacher {
  width: 640px;
}
#setInputTeacher .inputTeacher {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 30px;
}
#setInputTeacher .inputTeacher .name {
  height: 24px;
  font-size: 14px;
  color: #474c59;
  line-height: 24px;
  margin-right: 16px;
}
#setInputTeacher .inputTeacher .it-con {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#setInputTeacher .inputTeacher .it-con .person-list ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#setInputTeacher .inputTeacher .it-con .person-list ul li {
  border-radius: 2px;
  background: rgba(0, 132, 255, 0.12);
  padding-left: 6px;
  padding-right: 2px;
  height: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 12px;
}
#setInputTeacher .inputTeacher .it-con .person-list ul li span {
  font-size: 13px;
  color: #3a8bff;
}
#setInputTeacher .inputTeacher .it-con .person-list ul li em {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background: url(../images/close-small.png) no-repeat center;
}
#setInputTeacher .inputTeacher .it-con .person-list ul li.selects {
  border-radius: 2px;
  border: 1px solid #8CBBFF;
  width: 62px;
  height: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding-left: 25px;
  background: url(../images/plus-iconss.png) no-repeat 12px center;
  cursor: pointer;
}
#setInputTeacher .inputTeacher .it-con .person-list ul li.selects span {
  font-size: 12px;
  color: #3a8bff;
}
#setInputTeacher .addType.flex-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#setInputTeacher .addType.flex-item .layui-form-item {
  margin-right: 24px;
}
#setInputTeacher .addType.flex-item .layui-form-item .w220 {
  width: 220px;
}
#setInputTeacher .addType.flex-item .layui-form-item:last-child {
  margin-right: 0;
}
#setInputTeacher .addType.flex-item .layui-form-item .layui-form-label {
  width: auto;
}
#setInputTeacher .addType.flex-item .layui-form-item .layui-input-block .input {
  position: relative;
}
#setInputTeacher .addType.flex-item .layui-form-item .layui-input-block .input .search {
  position: absolute;
  top: 1px;
  right: 1px;
  width: 32px;
  height: 32px;
  background: url(../images/icon-search.png) no-repeat center;
  cursor: pointer;
}
#setInputTeacher .result-box .r-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
  width: 100%;
  height: 29px;
  margin-bottom: 30px;
}
#setInputTeacher .result-box .r-top .l-box {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#setInputTeacher .result-box .r-top .l-box .all-select {
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #4C88FF;
  width: 48px;
  height: 29px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  text-align: center;
  line-height: 27px;
  font-size: 12px;
  color: #4c88ff;
  font-weight: 500;
  margin-right: 12px;
}
#setInputTeacher .result-box .r-top .l-box .cancle-select {
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid rgba(255, 66, 68, 0.7);
  width: 72px;
  height: 29px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  text-align: center;
  line-height: 27px;
  font-size: 12px;
  color: #ff4d55;
  font-weight: 500;
  margin-right: 12px;
}
#setInputTeacher .result-box .r-top .r-box .refresh {
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #4C88FF;
  width: 48px;
  height: 29px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  text-align: center;
  line-height: 27px;
  font-size: 12px;
  color: #4c88ff;
  font-weight: 500;
  margin-right: 12px;
}
#setInputTeacher .result-box .r-con ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#setInputTeacher .result-box .r-con ul li {
  width: 166px;
  height: 32px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 15px;
  text-align: left;
  cursor: pointer;
  margin-right: 27px;
  margin-bottom: 28px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#setInputTeacher .result-box .r-con ul li .name {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  margin-right: 5px;
  flex-shrink: 0;
  white-space: nowrap;
}
#setInputTeacher .result-box .r-con ul li .links {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}
#setInputTeacher .result-box .r-con ul li.cur {
  border: 1px solid #1890ff;
  position: relative;
}
#setInputTeacher .result-box .r-con ul li.cur:after {
  position: absolute;
  content: '';
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: url(../images/selected-icons.png) no-repeat center;
}
#setInputTeacher .result-box .r-con ul li.cur .name {
  color: #1890ff;
}
#setInputTeacher .result-box .r-con ul li.cur .links {
  color: #1890ff;
}
#subitemadd {
  width: 640px;
}
#subitemadd .popup-con .layui-form .layui-form-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#subitemadd .popup-con .layui-form .layui-form-label {
  color: #474C59;
  width: 130px;
}
.content .disabled .import {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  text-shadow: none;
  box-shadow: none;
  cursor: default;
}
.content .disabled .export {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  text-shadow: none;
  box-shadow: none;
  cursor: default;
}
#gsbjsubitemTwo {
  width: 845px;
}
#gsbjsubitemTwo .popup-con .layui-form.flex-form {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .layui-form.flex-form .layui-form-item {
  flex: 1;
}
#gsbjsubitemTwo .popup-con .layui-form .layui-form-label {
  font-size: 16px;
  width: auto;
}
#gsbjsubitemTwo .popup-con .lable h3 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #131b26;
  line-height: 22px;
  margin-bottom: 16px;
}
#gsbjsubitemTwo .popup-con .lable h3 em {
  font-size: 16px;
  color: #e23131;
  display: inline-block;
  margin-right: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula {
  border-radius: 8px;
  border: 1px solid #8CBBFF;
  background: #F0F6FF;
  min-height: 80px;
  position: relative;
  margin-bottom: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .lable .formula:after {
  content: '';
  position: absolute;
  left: 86px;
  top: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  background-color: #8CBBFF;
}
#gsbjsubitemTwo .popup-con .lable .formula .name {
  width: 86px;
  flex-shrink: 0;
  text-align: center;
  font-size: 14px;
  color: #4e5969;
  font-style: normal;
  font-weight: 500;
  border-right: 1px solid #8CBBFF;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con {
  flex: 1;
  padding: 10px 16px;
  position: relative;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con:after {
  content: '1、使用参与计算的成绩分项 2、选择分项对应的计分级制 3、使用计算键盘可灵活配置计算规则 ';
  position: absolute;
  left: 15px;
  top: 50%;
  margin-top: -8px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con.cons:after {
  display: none;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con span {
  float: left;
  margin: 0 5px;
  padding: 0 14px;
  width: auto;
  height: 24px;
  background: #4D88FF;
  border-radius: 15px;
  text-align: center;
  line-height: 24px;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con .sign {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
  padding: 0;
  margin-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con .num {
  padding: 0;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
  margin-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .lable .formula .f-con .num.spot {
  margin: 0;
}
#gsbjsubitemTwo .popup-con .grade-keyboard {
  border-radius: 8px;
  background: #F7F8FA;
  padding: 24px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top {
  width: 100%;
  height: 20px;
  margin-bottom: 15px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .titles {
  font-size: 15px;
  color: #131b26;
  margin-right: 14px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .tips {
  border-radius: 9.6px;
  border: 1px solid #F2F2F2;
  background: #F2F4F7;
  padding: 0 10px;
  height: 20px;
  line-height: 20px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .tips span {
  display: inline-block;
  background: url(../images/tips-icons.png) no-repeat left center;
  font-size: 12px;
  color: #737b86;
  padding-left: 15px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch {
  position: absolute;
  top: 2px;
  right: 0;
  height: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch span {
  color: #8a8b99;
  font-size: 14px;
  line-height: 20px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch {
  margin-top: 0;
  height: 14px;
  line-height: 14px;
  min-width: 26px;
  padding: 0;
  margin-left: 10px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch i {
  width: 10px;
  height: 10px;
  left: 2px;
  top: 2px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch {
  background-color: #1A79FF;
  border-color: #1A79FF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch i {
  margin-left: 0;
  left: 14px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons {
  overflow-x: auto;
  padding-bottom: 5px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul {
  padding: 3px 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li {
  margin-bottom: 3px;
  width: 356px;
  flex-shrink: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  margin-right: 20px;
  height: 42px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 15px;
  border-radius: 4px;
  border: 1px solid #E4E8F0;
  background: #FFF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .layui-input,
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .layui-textarea,
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .layui-select {
  height: 28px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .name {
  background: url(../images/check-icon.png) no-repeat left center;
  font-size: 14px;
  color: #474c59;
  padding-left: 28px;
  cursor: pointer;
  margin-right: 10px;
  max-width: 70px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .name.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .select.w102 {
  width: 102px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard h3 {
  font-size: 15px;
  color: #131b26;
  line-height: 21px;
  margin-top: 2px;
  margin-bottom: 16px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con {
  overflow: hidden;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span {
  float: left;
  height: 42px;
  border: 1px solid #E4E8F0;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-bottom: 5px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  width: 130px;
  margin-right: 20px;
  line-height: 40px;
  padding: 0 14px;
  text-align: left;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span:nth-child(5n) {
  margin-right: 0;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign {
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.spot {
  position: relative;
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.spot:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 4px;
  height: 4px;
  background: #4E5969;
  margin-left: -2px;
  margin-top: -2px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.spot:hover:after {
  background-color: #4D88FF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.zero {
  width: 280px;
  height: 42px;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span:hover {
  background: #E1EBFF;
  color: #4D88FF;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span:last-child {
  margin-right: 0;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.lbracket {
  background: #FFFFFF url(../images/sign-lkh.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.lbracket:hover {
  background: #E1EBFF url(../images/sign-lkh-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.rbracket {
  background: #FFFFFF url(../images/sign-rkh.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.rbracket:hover {
  background: #E1EBFF url(../images/sign-rkh-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.delet {
  background: #FFFFFF url(../images/sign-back.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.delet:hover {
  background: #E1EBFF url(../images/sign-back-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-add {
  background: #FFFFFF url(../images/sign-add.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-add:hover {
  background: #E1EBFF url(../images/sign-add-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle {
  background: #FFFFFF url(../images/sign-jian.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle:hover {
  background: #E1EBFF url(../images/sign-jian-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-mul {
  background: #FFFFFF url(../images/sign-ceng.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-mul:hover {
  background: #E1EBFF url(../images/sign-ceng-cur.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-except {
  background: #FFFFFF url(../images/sign-chu.png) no-repeat 14px center;
}
#gsbjsubitemTwo .popup-con .grade-keyboard .keyboard .k-con span.sign-except:hover {
  background: #E1EBFF url(../images/sign-chu-cur.png) no-repeat 14px center;
}
.scrollBox .layui-form-select dl {
  position: fixed;
  left: auto;
  top: auto;
  margin-top: 4px;
  width: 110px;
  min-width: auto;
}
.scrollBox .layui-form-select dl {
  position: fixed;
  left: auto;
  top: auto;
  margin-top: 4px;
  width: 110px;
  min-width: auto;
}
.scrollBox .i-bottom .layui-form-select dl {
  width: 303px;
  min-width: auto;
}
.scrollBox .right .layui-form-select dl {
  width: 102px;
  min-width: auto;
}
.scrollBox .selectr .layui-form-select dl {
  width: 240px;
}
#gsbjsubitem {
  width: 840px;
}
#gsbjsubitem .popup-con .layui-form .layui-form-label {
  font-size: 16px;
  width: auto;
}
#gsbjsubitem .popup-con .lable h3 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #131b26;
  line-height: 22px;
  margin-bottom: 16px;
}
#gsbjsubitem .popup-con .lable h3 em {
  font-size: 16px;
  color: #e23131;
  display: inline-block;
  margin-right: 5px;
}
#gsbjsubitem .popup-con .lable .formula {
  border-radius: 8px;
  border: 1px solid #8CBBFF;
  background: #F0F6FF;
  min-height: 80px;
  position: relative;
  margin-bottom: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .lable .formula:after {
  content: '';
  position: absolute;
  left: 86px;
  top: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
  background-color: #8CBBFF;
}
#gsbjsubitem .popup-con .lable .formula .name {
  width: 86px;
  flex-shrink: 0;
  text-align: center;
  font-size: 14px;
  color: #4e5969;
  font-style: normal;
  font-weight: 500;
  border-right: 1px solid #8CBBFF;
}
#gsbjsubitem .popup-con .lable .formula .f-con {
  flex: 1;
  padding: 10px 16px;
  position: relative;
}
#gsbjsubitem .popup-con .lable .formula .f-con:after {
  content: '1、使用参与计算的成绩分项 2、选择分项对应的计分级制 3、使用计算键盘可灵活配置计算规则 ';
  position: absolute;
  left: 15px;
  top: 50%;
  margin-top: -8px;
}
#gsbjsubitem .popup-con .lable .formula .f-con.cons:after {
  display: none;
}
#gsbjsubitem .popup-con .lable .formula .f-con span {
  float: left;
  margin: 0 5px;
  padding: 0 14px;
  width: auto;
  height: 28px;
  background: #4D88FF;
  border-radius: 15px;
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 10px;
}
#gsbjsubitem .popup-con .lable .formula .f-con .sign {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
  padding: 0;
}
#gsbjsubitem .popup-con .lable .formula .f-con .num {
  padding: 0;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 24px;
  margin: 0 5px;
  width: auto;
  height: 24px;
  background: transparent;
  border-radius: 0;
}
#gsbjsubitem .popup-con .lable .formula .f-con .num.spot {
  margin: 0;
}
#gsbjsubitem .popup-con .grade-keyboard {
  border-radius: 8px;
  background: #F7F8FA;
  padding: 24px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top {
  width: 100%;
  height: 20px;
  margin-bottom: 15px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .titles {
  font-size: 15px;
  color: #131b26;
  margin-right: 14px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .tips {
  border-radius: 9.6px;
  border: 1px solid #F2F2F2;
  background: #F2F4F7;
  padding: 0 10px;
  height: 20px;
  line-height: 20px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .tips span {
  display: inline-block;
  background: url(../images/tips-icons.png) no-repeat left center;
  font-size: 12px;
  color: #737b86;
  padding-left: 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch {
  position: absolute;
  top: 2px;
  right: 0;
  height: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch span {
  color: #8a8b99;
  font-size: 14px;
  line-height: 20px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch {
  margin-top: 0;
  height: 14px;
  line-height: 14px;
  min-width: 26px;
  padding: 0;
  margin-left: 10px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-switch i {
  width: 10px;
  height: 10px;
  left: 2px;
  top: 2px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch {
  background-color: #1A79FF;
  border-color: #1A79FF;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-top .limit-switch .layui-form-onswitch i {
  margin-left: 0;
  left: 14px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons {
  overflow-x: auto;
  padding-bottom: 5px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item {
  width: 333px;
  flex-shrink: 0;
  margin-right: 67px;
  border-radius: 4px;
  border: 1px solid #E4E8F0;
  background: #FFF;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con {
  border-bottom: 1px solid #E4E8F0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .left {
  flex-shrink: 0;
  flex: 1;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 40px;
  padding: 0 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .left .name {
  background: url(../images/check-icon.png) no-repeat left center;
  font-size: 14px;
  color: #474c59;
  padding-left: 28px;
  cursor: pointer;
  height: 100%;
  line-height: 40px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .left .name.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right {
  flex-shrink: 0;
  width: 0;
  height: 117px;
  overflow-y: auto;
  display: none;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul {
  padding: 3px 0;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li {
  margin-bottom: 3px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li .name {
  background: url(../images/check-icon.png) no-repeat left center;
  font-size: 14px;
  color: #474c59;
  padding-left: 28px;
  cursor: pointer;
  margin-right: 10px;
  max-width: 70px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li .name.cur {
  background: url(../images/check-icon-cur.png) no-repeat left center;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right .select.w102 {
  width: 102px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item.showMore .i-con .left {
  width: 101px;
  border-right: 1px solid #E4E8F0;
  height: 117px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item.showMore .i-con .left .name {
  line-height: 117px;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item.showMore .i-con .right {
  width: 232px;
  display: block;
}
#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-bottom {
  padding: 3px 15px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard h3 {
  font-size: 15px;
  color: #131b26;
  line-height: 21px;
  margin-top: 2px;
  margin-bottom: 16px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con {
  overflow: hidden;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span {
  float: left;
  height: 42px;
  border: 1px solid #E4E8F0;
  border-radius: 4px;
  background-color: #FFFFFF;
  margin-bottom: 5px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  width: 130px;
  margin-right: 20px;
  line-height: 40px;
  padding: 0 14px;
  text-align: left;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span:nth-child(5n) {
  margin-right: 0;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign {
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.spot {
  position: relative;
  overflow: hidden;
  text-indent: 99em;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.spot:after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 4px;
  height: 4px;
  background: #4E5969;
  margin-left: -2px;
  margin-top: -2px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.spot:hover:after {
  background-color: #4D88FF;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.zero {
  width: 280px;
  height: 42px;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span:hover {
  background: #E1EBFF;
  color: #4D88FF;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span:last-child {
  margin-right: 0;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.lbracket {
  background: #FFFFFF url(../images/sign-lkh.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.lbracket:hover {
  background: #E1EBFF url(../images/sign-lkh-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.rbracket {
  background: #FFFFFF url(../images/sign-rkh.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.rbracket:hover {
  background: #E1EBFF url(../images/sign-rkh-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.delet {
  background: #FFFFFF url(../images/sign-back.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.delet:hover {
  background: #E1EBFF url(../images/sign-back-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-add {
  background: #FFFFFF url(../images/sign-add.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-add:hover {
  background: #E1EBFF url(../images/sign-add-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle {
  background: #FFFFFF url(../images/sign-jian.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-cancle:hover {
  background: #E1EBFF url(../images/sign-jian-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-mul {
  background: #FFFFFF url(../images/sign-ceng.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-mul:hover {
  background: #E1EBFF url(../images/sign-ceng-cur.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-except {
  background: #FFFFFF url(../images/sign-chu.png) no-repeat 14px center;
}
#gsbjsubitem .popup-con .grade-keyboard .keyboard .k-con span.sign-except:hover {
  background: #E1EBFF url(../images/sign-chu-cur.png) no-repeat 14px center;
}
